---
description: 
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Error handling

**Rule Name**: `frontend_error_handling`

## Error Handling & Toast Notifications

### Toast Patterns
```ts
// Success toast
toast({ 
  title: 'Success!',
  description: 'Operation completed successfully.'
})

// Error toast
toast({
  title: 'Error',
  description: error.message || 'Operation failed',
  variant: 'destructive',
})

// Warning toast
toast({
  title: 'Warning',
  description: 'Please check your input.',
  variant: 'destructive',
})

// Info toast
toast({
  title: 'Information',
  description: 'This is an informational message.',
})
```

### Error State Display
```vue
<template>
  <!-- Error boundary -->
  <div v-if="isError" class="flex items-center justify-center p-8">
    <div class="text-center">
      <BaseIcon name="XCircle" class="w-12 h-12 mx-auto mb-4 text-red-500" />
      <h3 class="mb-2 text-lg font-medium text-gray-900">Error Loading Data</h3>
      <p class="mb-4 text-sm text-gray-500">{{ error?.message || 'Something went wrong' }}</p>
      <Button @click="refetch">Try Again</Button>
    </div>
  </div>
  
  <!-- Empty state -->
  <div v-else-if="!isLoading && (!data || data.length === 0)" class="py-12 text-center">
    <BaseIcon name="Inbox" class="w-12 h-12 mx-auto mb-4 text-gray-400" />
    <h3 class="mb-2 text-lg font-medium text-gray-900">No items found</h3>
    <p class="text-sm text-gray-500">Get started by adding your first item.</p>
  </div>
</template>
```