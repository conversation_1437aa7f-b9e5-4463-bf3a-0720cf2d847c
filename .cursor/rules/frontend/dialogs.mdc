---
description:
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Frontend Dialogs System

**Rule Name**: `frontend_dialogs`

# Type-Safe Centralized Dialog System with Automatic Prop Extraction & Optimized Close

## Overview

Our dialog system provides **type-safe, centralized dialog management** with **automatic prop extraction** from Vue components and **optimized close functionality**. No manual prop definitions or close handlers needed!

## ✨ Key Features

- **🎯 Automatic Prop Extraction** - Props are extracted directly from Vue components
- **🔒 Full Type Safety** - TypeScript intellisense and validation
- **🚀 Zero Duplication** - No need to define props twice
- **📝 Self-Documenting** - Component props serve as the source of truth
- **⚡ Optimized Close** - Direct store communication without prop chains
- **🔄 Backward Compatible** - Supports both new and legacy patterns

## Quick Start

```typescript
import { useDialogStore, DIALOG_KEYS } from '@/stores/dialog'

const dialogStore = useDialogStore()

// Props are automatically extracted from the component!
dialogStore.openDialog(DIALOG_KEYS.AGENT_SETTINGS, {
  agentUid: 'agent-123', // Required - extracted from component
  section: 'behavior', // Optional - with full intellisense
})
```

## How Automatic Prop Extraction Works

### 1. Define Props in Your Component

```vue
<!-- AgentSettings.vue -->
<template>
  <BaseDialog
    :open="true"
    :title="title"
    :dialogId="props.dialogId"
  >
    <!-- Your dialog content -->
  </BaseDialog>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string                    // Required prop
  section?: 'general' | 'behavior'   // Optional prop with enum
  onCloseDialog?: () => void          // Auto-injected (backward compatibility)
  dialogId?: string                   // Auto-injected (optimized close)
}>()
</script>
```

### 2. Props Are Automatically Extracted

```typescript
// In stores/dialog.ts - NO MANUAL DEFINITION NEEDED!
export type AgentSettingsProps = ExtractComponentProps<typeof AgentSettingsComponent> & BaseDialogProps

// TypeScript automatically knows:
// - agentUid is required (string)
// - section is optional ('general' | 'behavior')
// - onCloseDialog is auto-injected by the store (backward compatibility)
// - dialogId is auto-injected by the store (optimized close)
```

### 3. Store Auto-Injects Close Functionality

```typescript
// Dialog store automatically injects both close methods
const dialogProps = {
  ...props,
  dialogId: instanceId,                        // Direct dialog ID for optimized close
  onCloseDialog: () => closeDialog(instanceId), // Backward compatibility
} as DialogPropsMap[K]
```

### 4. Use with Full Type Safety

```typescript
// ✅ TypeScript enforces required props
dialogStore.openDialog(DIALOG_KEYS.AGENT_SETTINGS, {
  agentUid: 'agent-123', // Required - won't compile without this
})

// ✅ TypeScript provides intellisense for optional props
dialogStore.openDialog(DIALOG_KEYS.AGENT_SETTINGS, {
  agentUid: 'agent-123',
  section: 'behavior', // Intellisense shows: 'general' | 'behavior'
})
```

## ⚡ Optimized Close Functionality

### How Close Optimization Works

The dialog system now uses **direct store communication** for closing dialogs, eliminating unnecessary prop chains.

#### **Before (Prop Chain)**
```
User Action → BaseDialog → props.onCloseDialog → Component.closeDialog → props.onCloseDialog → Dialog Store
```

#### **After (Direct Store Access)**
```
User Action → BaseDialog → dialogStore.closeDialog(dialogId) → Dialog Store
```

### BaseDialog Auto-Close Implementation

```typescript
// BaseDialog automatically handles close with priority system
function handleOpenChange(value: boolean) {
  if (!value) {
    // Priority 1: Use direct dialog store if we have dialogId (OPTIMIZED)
    if (props.dialogId) {
      dialogStore.closeDialog(props.dialogId)
    }
    // Priority 2: Fallback to onCloseDialog prop (BACKWARD COMPATIBILITY)
    else if (props.onCloseDialog) {
      props.onCloseDialog()
    }
  }
}
```

### Component Implementation

```vue
<!-- Your dialog component -->
<template>
  <BaseDialog
    :open="true"
    :title="title"
    :dialogId="props.dialogId"  <!-- Pass dialog ID for optimized close -->
  >
    <!-- Dialog content -->
    <Button @click="closeDialog">Cancel</Button>  <!-- Still works! -->
  </BaseDialog>
</template>

<script setup lang="ts">
const props = defineProps<{
  // Your component props
  userId?: string
  mode: 'create' | 'edit'

  // Auto-injected by dialog store
  dialogId?: string          // For optimized close
  onCloseDialog?: () => void  // For backward compatibility
}>()

// This still works for programmatic closing
const closeDialog = () => {
  props.onCloseDialog?.()
}
</script>
```

### Benefits of Optimized Close

- ✅ **Eliminated prop chains** - No more passing close functions around
- ✅ **Direct store access** - BaseDialog talks directly to the store
- ✅ **Backward compatibility** - Still supports `onCloseDialog` for existing dialogs
- ✅ **Better performance** - Fewer function calls and indirection
- ✅ **Cleaner architecture** - Each component has a single responsibility

## Dialog Categories

### 🤖 Agent Dialogs

- `DIALOG_KEYS.AGENT_INSTALL` - Agent installation wizard
- `DIALOG_KEYS.AGENT_DESIGN` - Agent appearance and branding
- `DIALOG_KEYS.AGENT_SETTINGS` - Agent configuration
- `DIALOG_KEYS.AGENT_KNOWLEDGE` - Knowledge base management
- `DIALOG_KEYS.AGENT_TOOLS` - Tool configuration
- `DIALOG_KEYS.AGENT_INBOX` - Conversation management
- `DIALOG_KEYS.AGENT_INTEGRATION` - Third-party integrations
- `DIALOG_KEYS.AGENT_FEEDBACKS` - Feedback overview
- `DIALOG_KEYS.AGENT_FEEDBACKS_FORM` - Create/edit feedback
- `DIALOG_KEYS.AGENT_LEADS_FORM` - Lead management form

### 📱 App Dialogs

- `DIALOG_KEYS.ACCOUNT` - User account settings
- `DIALOG_KEYS.USERS` - User management overview
- `DIALOG_KEYS.USERS_FORM` - Create/edit user form
- `DIALOG_KEYS.SUBSCRIPTIONS` - Subscription management
- `DIALOG_KEYS.SUPPORT` - Support ticket system

### 🎨 Whitelabel Dialogs

- `DIALOG_KEYS.WHITELABEL` - Whitelabel settings overview
- `DIALOG_KEYS.WHITELABEL_COPYRIGHTS_FORM` - Copyright management
- `DIALOG_KEYS.WHITELABEL_DOMAINS_FORM` - Domain configuration
- `DIALOG_KEYS.WHITELABEL_KEYS_FORM` - API key management
- `DIALOG_KEYS.WHITELABEL_SMTPS_FORM` - SMTP configuration

## Type-Safe Examples

### Required Props

```typescript
// ✅ Agent Settings - requires agentId
dialogStore.openDialog(DIALOG_KEYS.AGENT_SETTINGS, {
  agentId: 'agent-123', // Required
  section: 'integrations', // Optional: 'general' | 'advanced' | 'integrations'
})

// ✅ Users Form - requires mode
dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, {
  mode: 'create', // Required: 'create' | 'edit'
  defaultRole: 'client', // Optional: 'owner' | 'manager' | 'client'
})
```

### Complex Form Props

```typescript
// ✅ Agent Feedbacks Form - complex props with validation
dialogStore.openDialog(DIALOG_KEYS.AGENT_FEEDBACKS_FORM, {
  agentId: 'agent-123', // Required
  mode: 'reply', // Required: 'create' | 'edit' | 'reply'
  feedbackId: 'feedback-456', // Optional
  parentFeedbackId: 'parent-789', // Optional (for replies)
})

// ✅ Whitelabel Domain Form
dialogStore.openDialog(DIALOG_KEYS.WHITELABEL_DOMAINS_FORM, {
  mode: 'edit', // Required: 'create' | 'edit'
  domainId: 'domain-123', // Optional
  parentDomainId: 'parent-456', // Optional
})
```

### Optional Props Only

```typescript
// ✅ Account Dialog - all props optional
dialogStore.openDialog(DIALOG_KEYS.ACCOUNT, {
  section: 'security', // Optional: 'profile' | 'security' | 'preferences'
})

// ✅ Support Dialog
dialogStore.openDialog(DIALOG_KEYS.SUPPORT, {
  ticketId: 'ticket-789', // Optional
  category: 'technical', // Optional: 'technical' | 'billing' | 'general'
})
```

## Advanced Usage

### Multiple Instances

```typescript
// Form dialogs allow multiple instances
const instance1 = dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, { mode: 'create' })
const instance2 = dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, { mode: 'edit', userId: '123' })

// Main dialogs auto-close previous instances
dialogStore.openDialog(DIALOG_KEYS.USERS) // Only one can be open
```

### Dialog Management

```typescript
// Close specific dialog
dialogStore.closeDialog(instanceId)

// Close all instances of a dialog type
dialogStore.closeDialogByKey(DIALOG_KEYS.USERS_FORM)

// Check if dialog type is open
const isOpen = dialogStore.isDialogTypeOpen(DIALOG_KEYS.USERS)

// Get all instances of a dialog type
const instances = dialogStore.getDialogInstances(DIALOG_KEYS.USERS_FORM)
```

## Adding New Dialogs (Super Easy with Auto-Extraction!)

### 1. Create Your Dialog Component

```vue
<!-- NewFeature.vue -->
<template>
  <BaseDialog
    :open="true"
    :title="title"
    :dialogId="props.dialogId"
  >
    <!-- Your dialog content -->
    <Button @click="closeDialog">Cancel</Button>
  </BaseDialog>
</template>

<script setup lang="ts">
// Define your props - they'll be automatically extracted!
const props = defineProps<{
  featureId: string           // Required
  mode: 'view' | 'edit'      // Required with enum
  section?: string           // Optional

  // Auto-injected by dialog store
  onCloseDialog?: () => void  // Backward compatibility
  dialogId?: string          // Optimized close
}>()

const title = computed(() => `${props.mode === 'edit' ? 'Edit' : 'View'} Feature`)

// Optional: programmatic close (Cancel button, form submission, etc.)
const closeDialog = () => {
  props.onCloseDialog?.()
}
</script>
```

### 2. Add to Dialog Store (5 simple steps)

```typescript
// In stores/dialog.ts

// Step 1: Add dialog key
export const DIALOG_KEYS = {
  // ... existing keys
  NEW_FEATURE: 'app.new-feature',
} as const

// Step 2: Import component for prop extraction
import type NewFeatureComponent from '@/components/NewFeature.vue'

// Step 3: Add prop type extraction
export type NewFeatureProps = ExtractComponentProps<typeof NewFeatureComponent> & BaseDialogProps

// Step 4: Add to props map
export interface DialogPropsMap {
  // ... existing mappings
  'app.new-feature': NewFeatureProps
}

// Step 5: Add configuration
export const dialogConfigs: Record<DialogKey, DialogConfig> = {
  // ... existing configs
  [DIALOG_KEYS.NEW_FEATURE]: {
    key: DIALOG_KEYS.NEW_FEATURE,
    component: defineAsyncComponent(() => import('@/components/NewFeature.vue')),
    allowMultiple: false,
  },
}
```

### 3. Use with Automatic Type Safety

```typescript
// ✅ Props automatically extracted from component!
dialogStore.openDialog(DIALOG_KEYS.NEW_FEATURE, {
  featureId: 'feature-123', // Required - TypeScript enforces
  mode: 'edit', // Required - intellisense shows 'view' | 'edit'
  section: 'advanced', // Optional - works seamlessly
})
```

## 🎉 Benefits of the Complete System

### ✅ **Automatic Prop Extraction**
- **No duplication** - Props defined once in the component
- **Always in sync** - Component props are the source of truth
- **Better developer experience** - Less boilerplate code
- **Type safety guaranteed** - TypeScript extracts exact prop types

### ✅ **Optimized Close Functionality**
- **Eliminated prop chains** - Direct store communication
- **Better performance** - Fewer function calls and indirection
- **Cleaner architecture** - Single responsibility principle
- **Backward compatible** - Supports both new and legacy patterns

### ✅ **Enterprise-Grade Features**
- **20 dialogs** with automatic prop extraction
- **Full type safety** with intellisense
- **Multiple instance support** - Open multiple dialogs simultaneously
- **Centralized management** - All dialogs in one place
- **Production ready** - Battle-tested and optimized

## Best Practices

1. **Always use DIALOG_KEYS** - Never hardcode dialog key strings
2. **Define clear prop interfaces** - Make required vs optional props obvious
3. **Use descriptive prop names** - Follow existing naming conventions
4. **Group related dialogs** - Use consistent key prefixes (agent., app., etc.)
5. **Test with TypeScript** - Ensure all prop combinations compile correctly
6. **Use `:dialogId="props.dialogId"`** - For optimized close functionality
7. **Keep backward compatibility** - Support both close methods when needed

## Summary

This dialog system provides:
- **🎯 Automatic prop extraction** from Vue components
- **⚡ Optimized close functionality** with direct store communication
- **🔒 Full type safety** with TypeScript intellisense
- **🚀 Zero duplication** - props defined once
- **📝 Self-documenting** - component props are the source of truth
- **🔄 Backward compatible** - supports legacy patterns
- **⚙️ Production ready** - enterprise-grade architecture

The result is a **maintainable, scalable, and developer-friendly** dialog system that eliminates boilerplate while providing maximum type safety and performance.