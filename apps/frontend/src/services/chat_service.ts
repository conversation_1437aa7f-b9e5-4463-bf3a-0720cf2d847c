import { queryClient } from '@/plugins/tanstack-query'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useApi } from '@/plugins/api'
import { type Ref, computed } from 'vue'
import type { RequestFilters } from '@/types/filters'
import type { ChatCreatePayload } from '@/types'

export const queryChats = (filters?: Ref<RequestFilters>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['chats'],
    queryFn: () => api.chats.$get(filters?.value ? { query: filters.value } : {}).unwrap(),
  })
}

export const queryChatById = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['chats', uid.value],
    queryFn: () => api.chats({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

export const queryChatMenuHistory = (agentUid: Ref<string | undefined>, search?: string) => {
  const api = useApi()

  return useQuery({
    queryKey: ['chats', 'menu-history', search],
    queryFn: () => {
      const query = search ? { search } : {}

      return api.chats['menu-history']({ agent_uid: agentUid.value! }).$get({ query }).unwrap()
    },
    enabled: computed(() => !!agentUid.value),
  })
}

export const queryChatInboxHistory = (
  agentUid: Ref<string | undefined>,
  filters?: Ref<RequestFilters>
) => {
  const api = useApi()

  return useQuery({
    queryKey: ['chats', 'inbox-history'],
    queryFn: () => {
      const payload = filters?.value || {}

      return api.chats['inbox-history']({ agent_uid: agentUid.value! })
        .$get({ query: payload })
        .unwrap()
    },
    enabled: computed(() => !!agentUid.value),
  })
}

export const mutateChat = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: ChatCreatePayload) => {
      if (uid.value) {
        return api.chats({ uid: uid.value }).$put(payload).unwrap()
      }

      return api.chats.$post(payload).unwrap()
    },
    onSuccess: async () => {
      if (uid.value) {
        queryClient.invalidateQueries({ queryKey: ['chats', uid.value] })
      }

      queryClient.invalidateQueries({ queryKey: ['chats'] })
      queryClient.invalidateQueries({ queryKey: ['chats', 'inbox-history'] })
      await queryClient.invalidateQueries({ queryKey: ['chats', 'menu-history'] })
    },
  })
}

export const mutateChatDestroy = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.chats({ uid: uid.value! }).$delete().unwrap(),
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['chats'] })
      queryClient.invalidateQueries({ queryKey: ['chats', 'inbox-history'] })
      await queryClient.invalidateQueries({ queryKey: ['chats', 'menu-history'] })
    },
  })
}

export const mutateChatArchive = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.chats.archive({ uid: uid.value! }).$get().unwrap(),
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['chats'] })
      queryClient.invalidateQueries({ queryKey: ['chats', 'menu-history'] })
      await queryClient.invalidateQueries({ queryKey: ['chats', 'inbox-history'] })
    },
  })
}

export const mutateChatAppendMetadata = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: object) => api.chats.metadata({ uid: uid.value! }).$put(payload).unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chats', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['chats', 'menu-history'] })
      queryClient.invalidateQueries({ queryKey: ['chats', 'inbox-history'] })
    },
  })
}
