import { queryClient } from '@/plugins/tanstack-query'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useApi } from '@/plugins/api'
import { type Ref, computed } from 'vue'
import type { AgentCreatePayload, AgentUpdatePayload, AgentUpdateOnboardingPayload } from '@/types'
import type { RequestFilters } from '@/types/filters'

export const queryAgents = (filters?: Ref<RequestFilters>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['agents', filters?.value],
    queryFn: () => api.agents.$get(filters?.value ? { query: filters.value } : {}).unwrap(),
  })
}

export const queryAgentById = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['agents', uid.value],
    queryFn: () => api.agents({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

export const queryAgentFactorySettings = () => {
  const api = useApi()

  return useQuery({
    queryKey: ['agents', 'factory-settings'],
    queryFn: () => api.agents['factory-settings'].$get().unwrap(),
  })
}

export const queryAgentFactoryTools = () => {
  const api = useApi()

  return useQuery({
    queryKey: ['agents', 'factory-tools'],
    queryFn: () => api.agents['factory-tools'].$get().unwrap(),
  })
}

// --- Mutations ---
export const mutateAgentCreate = () => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: AgentCreatePayload) => api.agents.$post(payload).unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

export const mutateAgentUpdate = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: AgentUpdatePayload) =>
      api.agents({ uid: uid.value! }).$put(payload).unwrap(),
    onSuccess: (_data, _variables, _context) => {
      queryClient.invalidateQueries({ queryKey: ['agents', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

export const mutateAgentDelete = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.agents({ uid: uid.value! }).$delete().unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

export const mutateAgentDuplicate = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.agents.duplicate({ uid: uid.value! }).$get().unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

export const mutateAgentDuplicateWithTraining = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.agents['duplicate-with-training']({ uid: uid.value! }).$get().unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}

export const mutateAgentUpdateImage = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: FormData) =>
      api.agents['update-image']({ uid: uid.value! })
        .$put(payload as any)
        .unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents', uid.value] })
    },
  })
}

export const mutateAgentUpdateOnboarding = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: AgentUpdateOnboardingPayload) =>
      api.agents['update-onboarding']({ uid: uid.value! }).$put(payload).unwrap(), // Wrap in body and cast for now
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['agents'] })
    },
  })
}
