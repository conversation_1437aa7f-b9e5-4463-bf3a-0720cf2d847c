<script setup lang="ts">
import { queryChatMenuHistory, mutateChat } from '@/services/chat_service'
import { queryClient } from '@/plugins/tanstack-query'
import { ensureAuthMe } from '@/services/auth_service'

// Composables
const { isMobile } = useSidebar()
const appStore = useAppStore()
const { toast } = useToast()

// Refs
const isCreatingNewChat = ref(false)

// Computed
const currentAgentUid = computed(() => appStore.currentAgentUid || undefined)

// Queries
const me = await ensureAuthMe()
const { data: chatHistory, isLoading } = queryChatMenuHistory(currentAgentUid)

const groupedChats = computed(() => {
  const result = [
    {
      title: 'Today',
      icon: 'Clock',
      isActive: true,
      items: chatHistory.value?.today || [],
    },
    {
      title: 'Yesterday',
      icon: 'Calendar',
      isActive: false,
      items: chatHistory.value?.yesterday || [],
    },
    {
      title: 'This Week',
      icon: 'CalendarDays',
      isActive: false,
      items: chatHistory.value?.week || [],
    },
    {
      title: 'This Month',
      icon: 'Calendar',
      isActive: false,
      items: chatHistory.value?.month || [],
    },
    {
      title: 'Older',
      icon: 'History',
      isActive: false,
      items: chatHistory.value?.older || [],
    },
  ]

  return result
})

const displayData = computed(() => {
  if (!currentAgentUid.value || isLoading.value) {
    return []
  }

  return groupedChats.value
})

const initialActiveTitle = displayData.value.find((d) => d.isActive)?.title ?? null

const openItemTitle = computed({
  get: () => appStore.sidebarItemState ?? initialActiveTitle,
  set: (value) => appStore.setSidebarItemState(value),
})
const newChatMutation = mutateChat(ref(undefined))

async function handleNewChat() {
  if (isCreatingNewChat.value) {
    return
  }

  isCreatingNewChat.value = true

  try {
    await newChatMutation.mutateAsync({
      agent_uid: currentAgentUid.value!,
      user_uid: me.user.uid,
      label: 'New chat',
      loaded_context: '',
      loaded_variables: {},
      loaded_user: {},
    })

    toast({
      title: 'Chat created',
      description: 'New chat has been created successfully',
    })
  } catch (err: any) {
    toast({
      title: 'Failed to create chat',
      description: err.message || 'Unable to create new chat',
      variant: 'destructive',
    })
  } finally {
    console.log('Setting isCreatingNewChat to false')
    isCreatingNewChat.value = false
  }
}

function handleToggle(itemTitle: string) {
  const newValue = openItemTitle.value === itemTitle ? null : itemTitle

  appStore.setSidebarItemState(newValue)
}

watchEffect(() => {
  if (isCreatingNewChat.value && openItemTitle.value !== 'Today') {
    appStore.setSidebarItemState('Today')
  }
})
</script>

<template>
  <!-- No Agent Selected State -->
  <template v-if="!currentAgentUid">
    <SidebarGroup>
      <SidebarGroupLabel>
        <BaseIcon name="MessageSquare" class="mr-1" />
        Select an Agent
      </SidebarGroupLabel>
      <SidebarMenu>
        <SidebarMenuItem>
          <div class="px-2 py-1 text-xs text-muted-foreground">
            Choose an agent to view chat history
          </div>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  </template>

  <!-- Loading State -->
  <template v-else-if="isLoading">
    <SidebarGroup>
      <SidebarGroupLabel>
        <div class="flex items-center">
          <div class="w-4 h-4 mr-1 bg-gray-200 rounded animate-pulse"></div>
          <div class="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </SidebarGroupLabel>
      <SidebarMenu>
        <SidebarMenuItem v-for="i in 3" :key="i">
          <div class="px-2 py-1">
            <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  </template>

  <!-- Chat History -->
  <template v-else>
    <SidebarGroup>
      <SidebarGroupContent>
        <SidebarMenu>
          <SidebarMenuItem class="mb-2">
            <SidebarMenuButton
              @click="handleNewChat"
              :disabled="isCreatingNewChat"
              :class="[
                'transition-all duration-200',
                isCreatingNewChat && 'bg-sidebar-accent text-sidebar-accent-foreground',
              ]"
            >
              <BaseIcon
                :name="isCreatingNewChat ? 'Loader2' : 'Plus'"
                :class="[
                  'transition-all duration-200 size-4',
                  isCreatingNewChat
                    ? 'animate-spin'
                    : 'group-hover:scale-110 group-hover:rotate-90',
                ]"
              />
              <span class="font-medium transition-all duration-200">
                {{ isCreatingNewChat ? 'Creating chat...' : 'New chat' }}
              </span>
              <div v-if="isCreatingNewChat" class="ml-auto">
                <div class="flex space-x-1">
                  <div class="w-1 h-1 bg-current rounded-full animate-bounce"></div>
                  <div
                    class="w-1 h-1 bg-current rounded-full animate-bounce"
                    style="animation-delay: 0.1s"
                  ></div>
                  <div
                    class="w-1 h-1 bg-current rounded-full animate-bounce"
                    style="animation-delay: 0.2s"
                  ></div>
                </div>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarInput id="search" placeholder="Search history..." class="pl-8" />
            <BaseIcon
              name="Search"
              class="absolute -translate-y-1/2 opacity-50 pointer-events-none select-none left-2 top-1/2 size-4"
            />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>

    <Collapsible
      v-for="item in displayData"
      :key="item.title"
      :open="openItemTitle === item.title"
      class="group/collapsible"
    >
      <SidebarGroup>
        <SidebarGroupLabel asChild>
          <CollapsibleTrigger @click="handleToggle(item.title)">
            <BaseIcon :name="item.icon" class="mr-1" />
            {{ item.title }}

            <BaseIcon
              name="ChevronDown"
              class="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180"
            />
          </CollapsibleTrigger>
        </SidebarGroupLabel>

        <CollapsibleContent>
          <SidebarMenu>
            <!-- Loading skeleton for new chat creation -->
            <SidebarMenuItem
              v-if="isCreatingNewChat && item.title === 'Today'"
              data-sidebar="menu-item"
            >
              <div class="px-2 py-1">
                <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </SidebarMenuItem>

            <!-- Empty state for sections with no items -->
            <SidebarMenuItem
              v-if="!isCreatingNewChat && item.items.length === 0"
              data-sidebar="menu-item"
            >
              <div class="flex items-center px-2 py-3 text-xs text-muted-foreground/60">
                <BaseIcon name="MessageCircle" class="w-3 h-3 mr-2 opacity-40" />
                <span class="italic">No chats {{ item.title.toLowerCase() }}</span>
              </div>
            </SidebarMenuItem>

            <SidebarMenuItem
              v-for="subItem in item.items"
              :key="subItem.label"
              data-sidebar="menu-item"
              class="relative group/menu-item"
            >
              <SidebarMenuButton class="transition-all duration-200 hover:bg-sidebar-accent/50">
                <BaseIcon name="MessageSquare" class="w-4 h-4 mr-2 opacity-60" />
                <span class="truncate">{{ subItem.label || 'New Chat' }}</span>
                <div
                  class="w-2 h-2 ml-auto transition-opacity duration-200 bg-green-500 rounded-full opacity-0 group-hover/menu-item:opacity-100"
                ></div>
              </SidebarMenuButton>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <SidebarMenuAction
                    show-on-hover
                    class="transition-all duration-200 hover:bg-sidebar-accent"
                  >
                    <BaseIcon name="MoreHorizontal" />
                    <span class="sr-only">More</span>
                  </SidebarMenuAction>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  class="w-40 rounded-lg"
                  :side="isMobile ? 'bottom' : 'right'"
                  :align="isMobile ? 'end' : 'start'"
                >
                  <DropdownMenuItem class="hover:bg-sidebar-accent">
                    <BaseIcon name="Pin" class="w-4 h-4 mr-2 text-muted-foreground" />
                    <span>Pin chat</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem class="hover:bg-destructive/10 text-destructive">
                    <BaseIcon name="Trash2" class="w-4 h-4 mr-2" />
                    <span>Delete</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </CollapsibleContent>
      </SidebarGroup>
    </Collapsible>
  </template>
</template>
