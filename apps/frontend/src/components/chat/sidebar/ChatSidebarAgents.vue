<script setup lang="ts">
import { queryAgents } from '@/services/agent_service'
import { useDialogStore, DIALOG_KEYS } from '@/stores/dialog'
import type { Agent } from '@/types'

const { data: agents, isLoading } = queryAgents()
const appStore = useAppStore()
const dialogStore = useDialogStore()

const activeAgent = ref<Agent | null>(null)

// Set the first agent as active when agents are loaded, or restore from store
watchEffect(() => {
  if (agents.value?.data && agents.value.data.length > 0) {
    // Try to find the agent from store first
    if (appStore.currentAgentUid) {
      const storedAgent = agents.value.data.find((agent) => agent.uid === appStore.currentAgentUid)
      if (storedAgent) {
        activeAgent.value = storedAgent
        return
      }
    }

    // If no stored agent or stored agent not found, use first agent
    if (!activeAgent.value) {
      activeAgent.value = agents.value.data[0]
      appStore.setCurrentAgentUid(agents.value.data[0].uid)
    }
  }
})

function setActiveAgent(agent: Agent) {
  activeAgent.value = agent
  appStore.setCurrentAgentUid(agent.uid)
}

function openCreateAgentDialog() {
  dialogStore.openDialog(DIALOG_KEYS.AGENT_CREATE_FORM)
}

// Helper function to get agent display name
function getAgentDisplayName(agent: Agent): string {
  return agent.label || 'Unnamed Agent'
}

// Helper function to get agent icon
function getAgentIcon(agent: Agent): string {
  // Use a default icon based on agent purpose or fallback to a generic one
  const purposeIcons: Record<string, string> = {
    support: 'Headphones',
    sales: 'TrendingUp',
    ecommerce: 'ShoppingCart',
    marketing: 'Megaphone',
    education: 'GraduationCap',
    healthcare: 'Heart',
    real_estate: 'Home',
    personal_assistant: 'User',
    character: 'Smile',
    other: 'Bot',
  }
  return purposeIcons[agent.purpose] || 'Bot'
}
</script>

<template>
  <SidebarMenuItem>
    <!-- Loading State -->
    <template v-if="isLoading">
      <SidebarMenuButton size="lg" disabled>
        <div
          class="flex items-center justify-center bg-gray-200 rounded-lg aspect-square size-8 animate-pulse"
        >
          <!-- Icon skeleton -->
        </div>
        <div class="grid flex-1 text-sm leading-tight text-left">
          <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div class="w-4 h-4 ml-auto bg-gray-200 rounded animate-pulse"></div>
      </SidebarMenuButton>
    </template>

    <!-- No Agents State -->
    <template v-else-if="!agents?.data || agents.data.length === 0">
      <SidebarMenuButton
        size="lg"
        @click="openCreateAgentDialog"
        class="hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
      >
        <div
          class="flex items-center justify-center rounded-lg aspect-square size-8 bg-sidebar-primary text-sidebar-primary-foreground"
        >
          <BaseIcon name="Plus" class="size-4" />
        </div>
        <div class="grid flex-1 text-sm leading-tight text-left">
          <span class="font-semibold truncate">Create your first agent</span>
        </div>
      </SidebarMenuButton>
    </template>

    <!-- Agents Available -->
    <template v-else>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <div
              class="flex items-center justify-center rounded-lg aspect-square size-8 bg-sidebar-primary text-sidebar-primary-foreground"
            >
              <BaseIcon :name="activeAgent ? getAgentIcon(activeAgent) : 'Bot'" class="size-4" />
            </div>
            <div class="grid flex-1 text-sm leading-tight text-left">
              <span class="font-semibold truncate">
                {{ activeAgent ? getAgentDisplayName(activeAgent) : 'Select Agent' }}
              </span>
            </div>
            <BaseIcon name="ChevronDown" class="ml-auto" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          class="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          align="start"
          side="bottom"
          :side-offset="4"
        >
          <DropdownMenuLabel class="text-xs text-muted-foreground"> Agents </DropdownMenuLabel>
          <DropdownMenuItem
            v-for="agent in agents.data"
            :key="agent.uid"
            class="gap-2 p-2"
            @click="setActiveAgent(agent)"
          >
            <div class="flex items-center justify-center border rounded-sm size-6">
              <BaseIcon :name="getAgentIcon(agent)" class="size-4 shrink-0" />
            </div>
            {{ getAgentDisplayName(agent) }}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem class="gap-2 p-2" @click="openCreateAgentDialog">
            <div class="flex items-center justify-center border rounded-md size-6">
              <BaseIcon name="Plus" />
            </div>
            <div class="font-medium text-muted-foreground">Add agent</div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </template>
  </SidebarMenuItem>
</template>
