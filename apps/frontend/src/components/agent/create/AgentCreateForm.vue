<template>
  <BaseDialog
    :icon="ICONS.AGENT_CREATE"
    :title="title"
    :description="description"
    :dialogId="props.dialogId"
  >
    <BaseForm :isLoading="false" :isSubmitting="isSubmitting" :inputs="5" @submit="onSubmit">
      <!-- Agent Label -->
      <FormField v-slot="{ componentField }" name="label">
        <FormItem>
          <FormLabel>Name</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="Enter agent name" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <!-- Agent Purpose -->
      <FormField v-slot="{ componentField }" name="purpose">
        <FormItem>
          <FormLabel>Purpose</FormLabel>
          <FormControl>
            <Select v-bind="componentField" @update:model-value="handlePurposeChange">
              <SelectTrigger>
                <SelectValue placeholder="Select agent purpose" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="purpose in AGENT_PURPOSES"
                  :key="purpose.value"
                  :value="purpose.value"
                >
                  {{ purpose.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </FormControl>
          <FormDescription>
            Choose the primary purpose of your agent to get tailored instruction suggestions
          </FormDescription>
          <FormMessage />
        </FormItem>
      </FormField>

      <!-- Agent Instructions/Purpose Description -->
      <FormField v-slot="{ componentField }" name="prompt_system">
        <FormItem>
          <FormLabel>Instructions</FormLabel>
          <FormControl>
            <Textarea v-bind="componentField" :placeholder="instructionPlaceholder" :rows="6" />
          </FormControl>
          <FormDescription>
            Tell your agent what it should do, how it should respond, and any specific guidelines it
            should follow.
          </FormDescription>
          <FormMessage />
        </FormItem>
      </FormField>

      <!-- Conversation Starters -->
      <FormItem class="space-y-4">
        <div>
          <label
            class="text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Conversation Starters
          </label>
          <p class="text-sm text-muted-foreground">
            Add up to 4 greeting messages that users can click to start conversations
          </p>
        </div>

        <div class="space-y-2">
          <div
            v-for="(starter, index) in conversationStarters"
            :key="index"
            class="flex items-center gap-2"
          >
            <Input
              v-model="starter.text"
              :placeholder="`Conversation starter ${index + 1}`"
              class="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              size="icon"
              @click="removeConversationStarter(index)"
              :disabled="conversationStarters.length <= 1"
            >
              <BaseIcon name="X" class="w-4 h-4" />
            </Button>
          </div>

          <Button
            type="button"
            variant="outline"
            @click="addConversationStarter"
            :disabled="conversationStarters.length >= 4"
            class="w-full"
          >
            <BaseIcon name="Plus" class="w-4 h-4 mr-2" />
            Add Conversation Starter
          </Button>
        </div>
      </FormItem>

      <!-- Vision Capability Toggle -->
      <FormField v-slot="{ componentField }" name="is_vision_capable">
        <FormItem class="flex flex-row items-center justify-between p-4 border rounded-lg">
          <div class="space-y-0.5">
            <FormLabel class="text-base">Vision Capability</FormLabel>
            <FormDescription> Enable your agent to read and analyze images </FormDescription>
          </div>
          <FormControl>
            <Switch v-bind="componentField" />
          </FormControl>
        </FormItem>
      </FormField>

      <!-- Source Disclosure Toggle -->
      <FormField v-slot="{ componentField }" name="source_discloser">
        <FormItem class="flex flex-row items-center justify-between p-4 border rounded-lg">
          <div class="space-y-0.5">
            <FormLabel class="text-base">Source Disclosure</FormLabel>
            <FormDescription> Show information sources in agent responses </FormDescription>
          </div>
          <FormControl>
            <Switch v-bind="componentField" />
          </FormControl>
        </FormItem>
      </FormField>

      <template #footer>
        <Button type="button" variant="outline" @click="fillDemoData">
          <BaseIcon name="Shuffle" class="w-4 h-4 mr-2" />
          Fill Demo Data
        </Button>
        <Button type="submit" :disabled="isSubmitting">
          <BaseIcon v-if="isSubmitting" name="Loader2" class="w-4 h-4 mr-2 animate-spin" />
          Create Agent
        </Button>
      </template>
    </BaseForm>
  </BaseDialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { mutateAgentCreate } from '@/services/agent_service'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/shadcn/ui/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shadcn/ui/select'
import { Button } from '@/shadcn/ui/button'
import { Input } from '@/shadcn/ui/input'
import { Textarea } from '@/shadcn/ui/textarea'
import { Switch } from '@/shadcn/ui/switch'
import {
  ICONS,
  AGENT_PURPOSES,
  DEFAULT_CONVERSATION_STARTERS,
  AGENT_INSTRUCTION_TEMPLATES,
} from '@/constants'

// Props that can be passed from the dialog store
const props = defineProps<{
  dialogId: string
}>()

// Composables
const { toast } = useToast()
const dialogStore = useDialogStore()
const appStore = useAppStore()

// Conversation starters state
const conversationStarters = ref([...DEFAULT_CONVERSATION_STARTERS])

// Current selected purpose and instruction state
const selectedPurpose = ref<keyof typeof AGENT_INSTRUCTION_TEMPLATES | ''>('')
const instructionPlaceholder = computed(() =>
  !selectedPurpose.value
    ? 'Select a purpose above to get started with instructions...'
    : selectedPurpose.value === 'other'
      ? 'Describe what your agent should do and how it should behave...'
      : 'Instructions will be automatically filled based on your selected purpose. You can customize them as needed.'
)

// Form setup with validation
const { handleSubmit, isSubmitting, setFieldValue } = useForm({
  initialValues: {
    label: '',
    purpose: undefined,
    prompt_system: '',
    is_vision_capable: false,
    source_discloser: true,
  },
  validationSchema: toTypedSchema(
    z.object({
      label: z
        .string()
        .min(1, { message: 'Agent name is required' })
        .max(100, { message: 'Agent name must be less than 100 characters' }),
      purpose: z.enum(
        [
          'support',
          'ecommerce',
          'sales',
          'marketing',
          'education',
          'healthcare',
          'real_estate',
          'personal_assistant',
          'character',
          'other',
        ],
        { message: 'Please select a purpose for your agent' }
      ),
      prompt_system: z
        .string()
        .min(10, { message: 'Instructions must be at least 10 characters' })
        .max(2000, { message: 'Instructions must be less than 2000 characters' }),
      is_vision_capable: z.boolean(),
      source_discloser: z.boolean(),
    })
  ),
})

// Mutation
const mutation = mutateAgentCreate()

// Computed properties
const title = computed(() => 'Create New Agent')
const description = computed(() => 'Set up your AI agent with custom instructions and capabilities')

// Functions
function addConversationStarter() {
  if (conversationStarters.value.length < 4) {
    conversationStarters.value.push({ text: '' })
  }
}

function removeConversationStarter(index: number) {
  if (conversationStarters.value.length > 1) {
    conversationStarters.value.splice(index, 1)
  }
}

function handlePurposeChange(value: string) {
  if (value in AGENT_INSTRUCTION_TEMPLATES) {
    selectedPurpose.value = value as keyof typeof AGENT_INSTRUCTION_TEMPLATES
    // Automatically set the instruction template
    setFieldValue(
      'prompt_system',
      AGENT_INSTRUCTION_TEMPLATES[value as keyof typeof AGENT_INSTRUCTION_TEMPLATES]
    )
  }
}

function fillDemoData() {
  const demoNames = [
    'Support Assistant Pro',
    'Sales Helper Bot',
    'Customer Care Agent',
    'Product Guide AI',
    'Order Assistant',
    'Tech Support Bot',
  ]

  const demoStarters = [
    [
      { text: 'Hi! How can I help you today?' },
      { text: 'I need help with my order' },
      { text: 'Tell me about your products' },
      { text: 'I have a technical question' },
    ],
    [
      { text: 'Welcome! What brings you here today?' },
      { text: 'I want to learn more about pricing' },
      { text: 'Can you help me choose a product?' },
      { text: 'I need support with setup' },
    ],
    [
      { text: 'Hello! Ready to get started?' },
      { text: 'Show me your latest features' },
      { text: 'I need help with billing' },
      { text: 'How does this work?' },
    ],
  ]

  // Pick random demo data
  const randomName = demoNames[Math.floor(Math.random() * demoNames.length)]
  const randomPurpose = AGENT_PURPOSES[Math.floor(Math.random() * AGENT_PURPOSES.length)]
  const randomStarters = demoStarters[Math.floor(Math.random() * demoStarters.length)]

  // Set form values
  setFieldValue('label', randomName)
  setFieldValue('purpose', randomPurpose.value)
  setFieldValue('is_vision_capable', Math.random() > 0.5)
  setFieldValue('source_discloser', Math.random() > 0.3)

  // Set conversation starters
  conversationStarters.value = [...randomStarters]

  // Trigger purpose change to set instructions
  handlePurposeChange(randomPurpose.value)
}

// Form submission
const onSubmit = handleSubmit(async (values) => {
  try {
    // Filter out empty conversation starters
    const validStarters = conversationStarters.value
      .filter((starter) => starter.text.trim() !== '')
      .map((starter) => ({ text: starter.text.trim() }))

    // Create agent with all form values in one call
    const payload = {
      label: values.label,
      purpose: values.purpose,
      prompt_system: values.prompt_system,
      is_vision_capable: values.is_vision_capable,
      source_discloser: values.source_discloser,
      greetings: validStarters,
      how_did_you_find_us: 'other' as const, // Default value as required by backend
    }

    const newAgent = await mutation.mutateAsync(payload)

    toast({
      title: 'Agent created successfully',
      description: `${values.label} is ready to help your users!`,
    })

    // Set the new agent as active
    if (newAgent?.uid) {
      appStore.setCurrentAgentUid(newAgent.uid)
    }

    dialogStore.closeDialog(props.dialogId)
  } catch (err: any) {
    toast({
      title: 'Failed to create agent',
      description: err.message || 'Something went wrong while creating your agent',
      variant: 'destructive',
    })
  }
})
</script>
