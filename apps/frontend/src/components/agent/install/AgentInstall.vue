<template>
  <BaseDialog
    title="Install"
    description="Choose your preferred installation method below. We recommend the Bubble option for most websites."
    icon="Bot"
    :dialogId="dialogId"
  >
    <!-- No Agent Selected State -->
    <template v-if="!currentAgentUid">
      <div class="flex flex-col items-center justify-center py-8 text-center">
        <BaseIcon name="Bot" class="w-12 h-12 mb-4 text-muted-foreground" />
        <h3 class="text-lg font-semibold mb-2">No Agent Selected</h3>
        <p class="text-sm text-muted-foreground mb-4">
          Please select an agent to view installation options.
        </p>
      </div>
    </template>

    <!-- Installation Options -->
    <template v-else>
      <BaseTabs
        default-value="bubble"
        :tabs="[
          {
            value: 'bubble',
            label: 'Floating Bubble',
            icon: 'MessageSquare',
          },
          {
            value: 'window',
            label: 'In-Page',
            icon: 'LayoutTemplate',
          },
          {
            value: 'html',
            label: 'Iframe Tag',
            icon: 'Code2',
          },
          {
            value: 'url',
            label: 'Direct URL',
            icon: 'Link',
          },
          {
            value: 'qr',
            label: 'QR Code',
            icon: 'QrCode',
          },
        ]"
      >
        <TabsContent value="bubble">
          <AgentInstallBubble :agentUid="currentAgentUid" />
        </TabsContent>
        <TabsContent value="window">
          <AgentInstallWindow :agentUid="currentAgentUid" />
        </TabsContent>
        <TabsContent value="html">
          <AgentInstallHtml :agentUid="currentAgentUid" />
        </TabsContent>
        <TabsContent value="url">
          <AgentInstallUrl :agentUid="currentAgentUid" />
        </TabsContent>
        <TabsContent value="qr">
          <AgentInstallQrCode :agentUid="currentAgentUid" />
        </TabsContent>
      </BaseTabs>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
defineProps<{
  title?: string
  dialogId: string
}>()

// Get current agent ID from app store
const appStore = useAppStore()
const currentAgentUid = computed(() => appStore.currentAgentUid)
</script>

<style scoped>
@media (max-width: 640px) {
  :deep(.tabs-list) {
    overflow-x: auto;
    flex-wrap: nowrap;
    padding-bottom: 0.5rem;
  }

  :deep(.tabs-trigger) {
    flex-shrink: 0;
    white-space: nowrap;
  }
}
</style>
