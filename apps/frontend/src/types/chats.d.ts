import { InferRequestType, InferResponseType } from '@tuyau/client'
import { useApi } from '@/plugins/api'

const api = useApi()

export type Chats = InferResponseType<typeof api.chats.$get>
export type Chat = Chats['data'][number]
export type ChatCreatePayload = InferRequestType<typeof api.chats.$post>
export type ChatUpdatePayload = InferRequestType<
  ReturnType<ReturnType<typeof useApi>['chats']>['$put']
>
