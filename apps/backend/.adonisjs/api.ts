// @ts-nocheck
/* eslint-disable */
// --------------------------------------------------
// This file is auto-generated by <PERSON><PERSON><PERSON>. Do not edit manually !
// --------------------------------------------------

import type { MakeTuyauRequest, MakeNonSerializedTuyauResponse } from '@tuyau/utils/types'
import type { InferInput } from '@vinejs/vine/types'

type V2AuthLoginPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/auth/auth_validator.ts')['authLoginValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/auth/auth_controller.ts').default['login'], true>
}
type V2AuthRegisterPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/auth/auth_validator.ts')['authRegisterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/auth/auth_controller.ts').default['register'], true>
}
type V2AuthAccessgooglePost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/auth/auth_validator.ts')['authAccessWithGoogleValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/auth/auth_controller.ts').default['accessWithGoogle'], true>
}
type V2AuthAccesstokenIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/auth/auth_controller.ts').default['accessWithToken'], false>
}
type V2AuthLogoutGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/auth/auth_controller.ts').default['logout'], false>
}
type V2AuthMeGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/auth/auth_controller.ts').default['me'], false>
}
type V2AuthDomainGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/auth/auth_validator.ts')['authDomainValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/auth/auth_controller.ts').default['domain'], true>
}
type V2AuthResetpasswordPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/auth/auth_validator.ts')['authResetPasswordValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/auth/auth_controller.ts').default['resetPassword'], true>
}
type V2AuthChangepasswordIdPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/auth/auth_validator.ts')['authChangePasswordValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/auth/auth_controller.ts').default['changePassword'], true>
}
type V2HooksNotifyp6la4dmuczhmvw8vwqvhjqf8Post = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/hooks/notify_hooks_controller.ts').default['handle'], false>
}
type V2HooksWhatsappo9or0bwyffw5q8hqeauyqvxpPost = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/hooks/whatsapp_hooks_controller.ts').default['handle'], false>
}
type V2HooksStripelfgp1fby127t4fwyve3ykpkdPost = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/hooks/stripe_hooks_controller.ts').default['handle'], false>
}
type V2EmbedsAgentsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/embeds/embeds_controller.ts').default['getAgent'], false>
}
type V2EmbedsChatsPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/embeds/embed_validator.ts')['embedCreateChatValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/embeds/embeds_controller.ts').default['createChat'], true>
}
type V2EmbedsChatsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/embeds/embeds_controller.ts').default['getChat'], false>
}
type V2EmbedsFeedbacksIdPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/embeds/embed_validator.ts')['embedFeedbackValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/embeds/embeds_controller.ts').default['createFeedback'], true>
}
type V2EmbedsMessagesPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/embeds/embed_validator.ts')['embedCreateMessageValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/embeds/embeds_controller.ts').default['createMessage'], true>
}
type V2EmbedsVerifypasswordIdPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/embeds/embed_validator.ts')['embedVerifyPasswordValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/embeds/embeds_controller.ts').default['verifyPassword'], true>
}
type V2EmbedsStorefilesIdPost = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/embeds/embeds_controller.ts').default['storeFiles'], false>
}
type V2EmbedsAudioSttIdPost = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/embeds/embeds_controller.ts').default['stt'], false>
}
type V2EmbedsAudioTtsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/embeds/embeds_controller.ts').default['tts'], false>
}
type V2ModelsGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/models/models_controller.ts').default['index'], false>
}
type V2FirebaseSubscribePost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/firebase/firebase_validator.ts')['firebaseSubscribeValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/firebase/firebase_controller.ts').default['subscribe'], true>
}
type V2OpenProductsGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/open/open_controller.ts').default['products'], false>
}
type V2OpenPricingtableGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/open/open_controller.ts').default['getFullPricingTable'], false>
}
type V2OpenWritingGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/open/open_controller.ts').default['getWriting'], false>
}
type V2OpenGirlfriendsGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/open/open_controller.ts').default['getGirlfriends'], false>
}
type V2OpenTestsmtpPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/open/open_validation.ts')['openTestSmtpValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/open/open_controller.ts').default['testSMTP'], true>
}
type V2IdSubscriptionsGetembeddedcheckoutsessionIdIdGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/subscriptions/subscription_validator.ts')['embeddedCheckoutSessionValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/subscriptions/subscriptions_controller.ts').default['getEmbeddedCheckoutSession'], true>
}
type V2IdSubscriptionsGetportalsessionurlIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/subscriptions/subscriptions_controller.ts').default['getPortalSessionUrl'], false>
}
type V2IdAgentsDuplicateIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['duplicateWithoutTraining'], false>
}
type V2IdAgentsDuplicatewithtrainingIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['duplicateWithTraining'], false>
}
type V2IdAgentsUpdateimageIdPut = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['updateImage'], false>
}
type V2IdAgentsUpdateonboardingIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/agents/agent_validator.ts')['agentUpdateOnboardingValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['updateOnboarding'], true>
}
type V2IdAgentsFactorysettingsGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['getFactorySettings'], false>
}
type V2IdAgentsFactorytoolsGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['getFactoryTools'], false>
}
type V2IdAgentsGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['index'], true>
}
type V2IdAgentsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['show'], false>
}
type V2IdAgentsPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/agents/agent_validator.ts')['agentStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['store'], true>
}
type V2IdAgentsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/agents/agent_validator.ts')['agentUpdateValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['update'], true>
}
type V2IdAgentsIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/agents/agents_controller.ts').default['destroy'], false>
}
type V2IdChatsGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/chats/chats_controller.ts').default['index'], true>
}
type V2IdChatsMenuhistoryIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/chats/chats_controller.ts').default['menuHistory'], false>
}
type V2IdChatsInboxhistoryIdGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/chats/chat_validator.ts')['chatHistoryValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/chats/chats_controller.ts').default['inboxHistory'], true>
}
type V2IdChatsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/chats/chats_controller.ts').default['show'], false>
}
type V2IdChatsPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/chats/chat_validator.ts')['chatStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/chats/chats_controller.ts').default['store'], true>
}
type V2IdChatsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/chats/chat_validator.ts')['chatUpdateValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/chats/chats_controller.ts').default['update'], true>
}
type V2IdChatsIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/chats/chats_controller.ts').default['destroy'], false>
}
type V2IdChatsArchiveIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/chats/chats_controller.ts').default['archive'], false>
}
type V2IdChatsMetadataIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/chats/chat_validator.ts')['chatMetadataUpdateValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/chats/chats_controller.ts').default['appendMetadata'], true>
}
type V2IdProvidersGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['index'], true>
}
type V2IdProvidersIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['show'], false>
}
type V2IdProvidersPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['store'], true>
}
type V2IdProvidersIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerUpdateValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['update'], true>
}
type V2IdProvidersIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['destroy'], false>
}
type V2IdProvidersChangemodeIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerChangeModeValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['changeMode'], true>
}
type V2IdProvidersIngestUrlsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerIngestUrlsValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['ingestUrls'], true>
}
type V2IdProvidersIngestYoutubeIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerIngestYoutubeUrlsValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['ingestYoutube'], true>
}
type V2IdProvidersIngestDocumentsIdPut = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['ingestDocuments'], false>
}
type V2IdProvidersIngestMediasIdPut = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['ingestMedias'], false>
}
type V2IdProvidersIngestQuizzesIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerIngestQuizzesValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['ingestQuizzes'], true>
}
type V2IdProvidersIngestTextsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerIngestTextsValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['ingestTexts'], true>
}
type V2IdProvidersIngestProductsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerIngestProductsValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['ingestProducts'], true>
}
type V2IdProvidersIngestStorageIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerIngestStorageValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['ingestStorage'], true>
}
type V2IdProvidersFetchUrlsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerFetchUrlsValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['fetchUrls'], true>
}
type V2IdProvidersFetchSitemapIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/rag/providers/provider_validator.ts')['providerFetchSitemapValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['fetchSitemap'], true>
}
type V2IdProvidersTransmitIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/providers/providers_controller.ts').default['transmit'], false>
}
type V2IdSourcesGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/sources/sources_controller.ts').default['index'], true>
}
type V2IdSourcesIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/sources/sources_controller.ts').default['show'], false>
}
type V2IdSourcesRefreshIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/sources/sources_controller.ts').default['refresh'], false>
}
type V2IdSourcesIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/rag/sources/sources_controller.ts').default['destroy'], false>
}
type V2IdUsersGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/users/users_controller.ts').default['index'], true>
}
type V2IdUsersIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/users/users_controller.ts').default['show'], false>
}
type V2IdUsersPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/users/user_validator.ts')['userStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/users/users_controller.ts').default['store'], true>
}
type V2IdUsersIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/users/user_validator.ts')['userUpdateValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/users/users_controller.ts').default['update'], true>
}
type V2IdUsersIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/users/users_controller.ts').default['destroy'], false>
}
type V2IdUsersChangeinfosPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/users/user_validator.ts')['userChangeInfosValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/users/users_controller.ts').default['changeInfos'], true>
}
type V2IdUsersChangeemailPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/users/user_validator.ts')['userChangeEmailValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/users/users_controller.ts').default['changeEmail'], true>
}
type V2IdUsersChangepasswordPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/users/user_validator.ts')['userChangePasswordValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/users/users_controller.ts').default['changePassword'], true>
}
type V2IdUsersSendnewpasswordIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/users/users_controller.ts').default['sendNewPassword'], false>
}
type V2IdContactsGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/contacts/contacts_controller.ts').default['index'], true>
}
type V2IdContactsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/contacts/contacts_controller.ts').default['show'], false>
}
type V2IdContactsPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/contacts/contact_validator.ts')['contactStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/contacts/contacts_controller.ts').default['store'], true>
}
type V2IdContactsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/contacts/contact_validator.ts')['contactUpdateValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/contacts/contacts_controller.ts').default['update'], true>
}
type V2IdContactsIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/contacts/contacts_controller.ts').default['destroy'], false>
}
type V2IdAppsSettingsAutotopupPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/apps/app_validator.ts')['appAutoTopupValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/apps/apps_controller.ts').default['updateAutoTopup'], true>
}
type V2IdStatsAllIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/stats/stats_controller.ts').default['getAllStats'], false>
}
type V2IdWhatsappGetsessionIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whatsapp/whatsapp_controller.ts').default['getSession'], false>
}
type V2IdWhatsappDeletesessionIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whatsapp/whatsapp_controller.ts').default['deleteSession'], false>
}
type V2IdWhatsappGetqrcodeIdPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whatsapp/whatsapp_validation.ts')['whatsappGetQrCodeValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whatsapp/whatsapp_controller.ts').default['getQrCode'], true>
}
type V2IdFeedbacksMarkastrainedIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/feedbacks/feedbacks_controller.ts').default['markAsTrained'], false>
}
type V2IdFeedbacksGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/feedbacks/feedbacks_controller.ts').default['index'], true>
}
type V2IdFeedbacksIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/feedbacks/feedbacks_controller.ts').default['show'], false>
}
type V2IdFeedbacksIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/feedbacks/feedbacks_controller.ts').default['destroy'], false>
}
type V2IdCopyrightsGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/copyrights/copyrights_controller.ts').default['index'], true>
}
type V2IdCopyrightsPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/copyrights/copyright_validator.ts')['copyrightStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/copyrights/copyrights_controller.ts').default['store'], true>
}
type V2IdCopyrightsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/copyrights/copyrights_controller.ts').default['show'], false>
}
type V2IdCopyrightsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/copyrights/copyright_validator.ts')['copyrightStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/copyrights/copyrights_controller.ts').default['update'], true>
}
type V2IdCopyrightsIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/copyrights/copyrights_controller.ts').default['destroy'], false>
}
type V2IdCopyrightsAssignIdPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/copyrights/copyright_validator.ts')['copyrightAssignValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/copyrights/copyrights_controller.ts').default['assign'], true>
}
type V2IdCopyrightsUnassignIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/copyrights/copyrights_controller.ts').default['unassign'], false>
}
type V2IdCopyrightsPrimaryIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/copyrights/copyrights_controller.ts').default['setPrimary'], false>
}
type V2IdDomainsGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['index'], true>
}
type V2IdDomainsPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/domains/domain_validator.ts')['domainStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['store'], true>
}
type V2IdDomainsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['show'], false>
}
type V2IdDomainsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/domains/domain_validator.ts')['domainStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['update'], true>
}
type V2IdDomainsIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['destroy'], false>
}
type V2IdDomainsAssignIdPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/domains/domain_validator.ts')['domainAssignValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['assign'], true>
}
type V2IdDomainsUnassignIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['unassign'], false>
}
type V2IdDomainsPrimaryIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['setPrimary'], false>
}
type V2IdDomainsVerifyIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['verify'], false>
}
type V2IdDomainsUpdateimageIdPut = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/domains/domains_controller.ts').default['updateImage'], false>
}
type V2IdSmtpsGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/smtps/smtps_controller.ts').default['index'], true>
}
type V2IdSmtpsPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/smtps/smtp_validator.ts')['smtpStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/smtps/smtps_controller.ts').default['store'], true>
}
type V2IdSmtpsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/smtps/smtps_controller.ts').default['show'], false>
}
type V2IdSmtpsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/smtps/smtp_validator.ts')['smtpStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/smtps/smtps_controller.ts').default['update'], true>
}
type V2IdSmtpsIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/smtps/smtps_controller.ts').default['destroy'], false>
}
type V2IdSmtpsAssignIdPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/smtps/smtp_validator.ts')['smtpAssignValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/smtps/smtps_controller.ts').default['assign'], true>
}
type V2IdSmtpsUnassignIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/smtps/smtps_controller.ts').default['unassign'], false>
}
type V2IdSmtpsPrimaryIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/smtps/smtps_controller.ts').default['setPrimary'], false>
}
type V2IdSmtpsVerifyIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/smtps/smtps_controller.ts').default['verify'], false>
}
type V2IdKeysGetHead = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/validators.ts')['requestFilterValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['index'], true>
}
type V2IdKeysPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/keys/key_validator.ts')['keyStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['store'], true>
}
type V2IdKeysIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['show'], false>
}
type V2IdKeysIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/keys/key_validator.ts')['keyStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['update'], true>
}
type V2IdKeysIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['destroy'], false>
}
type V2IdKeysAssignIdPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/keys/key_validator.ts')['keyAssignValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['assign'], true>
}
type V2IdKeysUnassignIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['unassign'], false>
}
type V2IdKeysPrimaryIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['setPrimary'], false>
}
type V2IdKeysVerifyIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['verify'], false>
}
type V2IdKeysTestPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/whitelabel/keys/key_validator.ts')['keyTestValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/whitelabel/keys/keys_controller.ts').default['test'], true>
}
type V2IdToolsGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/tools/tools_controller.ts').default['index'], false>
}
type V2IdToolsIdGetHead = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/tools/tools_controller.ts').default['show'], false>
}
type V2IdToolsPost = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/tools/tool_validator.ts')['toolStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/tools/tools_controller.ts').default['store'], true>
}
type V2IdToolsIdPut = {
  request: MakeTuyauRequest<InferInput<typeof import('../app/modules/tools/tool_validator.ts')['toolStoreValidator']>>
  response: MakeNonSerializedTuyauResponse<import('../app/modules/tools/tools_controller.ts').default['update'], true>
}
type V2IdToolsToggleIdPatch = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/tools/tools_controller.ts').default['toggleStatus'], false>
}
type V2IdToolsIdDelete = {
  request: unknown
  response: MakeNonSerializedTuyauResponse<import('../app/modules/tools/tools_controller.ts').default['destroy'], false>
}
export interface ApiDefinition {
  'v2': {
    'auth': {
      'login': {
        '$url': {
        };
        '$post': V2AuthLoginPost;
      };
      'register': {
        '$url': {
        };
        '$post': V2AuthRegisterPost;
      };
      'access-google': {
        '$url': {
        };
        '$post': V2AuthAccessgooglePost;
      };
      'access-token': {
        ':token': {
          '$url': {
          };
          '$get': V2AuthAccesstokenIdGetHead;
          '$head': V2AuthAccesstokenIdGetHead;
        };
      };
      'logout': {
        '$url': {
        };
        '$get': V2AuthLogoutGetHead;
        '$head': V2AuthLogoutGetHead;
      };
      'me': {
        '$url': {
        };
        '$get': V2AuthMeGetHead;
        '$head': V2AuthMeGetHead;
      };
      'domain': {
        '$url': {
        };
        '$get': V2AuthDomainGetHead;
        '$head': V2AuthDomainGetHead;
      };
      'reset-password': {
        '$url': {
        };
        '$post': V2AuthResetpasswordPost;
      };
      'change-password': {
        ':token': {
          '$url': {
          };
          '$post': V2AuthChangepasswordIdPost;
        };
      };
    };
    'hooks': {
      'notify-p6LA4DmUcZHmvW8vWqVhjqf8': {
        '$url': {
        };
        '$post': V2HooksNotifyp6la4dmuczhmvw8vwqvhjqf8Post;
      };
      'whatsapp-O9or0bwyffW5Q8hQEaUYqvXp': {
        '$url': {
        };
        '$post': V2HooksWhatsappo9or0bwyffw5q8hqeauyqvxpPost;
      };
      'stripe-LfGP1fBY127T4fwyvE3YKpKD': {
        '$url': {
        };
        '$post': V2HooksStripelfgp1fby127t4fwyve3ykpkdPost;
      };
    };
    'embeds': {
      'agents': {
        ':agent_uid': {
          '$url': {
          };
          '$get': V2EmbedsAgentsIdGetHead;
          '$head': V2EmbedsAgentsIdGetHead;
        };
      };
      'chats': {
        '$url': {
        };
        '$post': V2EmbedsChatsPost;
        ':chat_uid': {
          '$url': {
          };
          '$get': V2EmbedsChatsIdGetHead;
          '$head': V2EmbedsChatsIdGetHead;
        };
      };
      'feedbacks': {
        ':message_uid': {
          '$url': {
          };
          '$post': V2EmbedsFeedbacksIdPost;
        };
      };
      'messages': {
        '$url': {
        };
        '$post': V2EmbedsMessagesPost;
      };
      'verify-password': {
        ':agent_uid': {
          '$url': {
          };
          '$post': V2EmbedsVerifypasswordIdPost;
        };
      };
      'store-files': {
        ':chat_uid': {
          '$url': {
          };
          '$post': V2EmbedsStorefilesIdPost;
        };
      };
      'audio': {
        'stt': {
          ':chat_uid': {
            '$url': {
            };
            '$post': V2EmbedsAudioSttIdPost;
          };
        };
        'tts': {
          ':message_uid': {
            '$url': {
            };
            '$get': V2EmbedsAudioTtsIdGetHead;
            '$head': V2EmbedsAudioTtsIdGetHead;
          };
        };
      };
    };
    'models': {
      '$url': {
      };
      '$get': V2ModelsGetHead;
      '$head': V2ModelsGetHead;
    };
    'firebase': {
      'subscribe': {
        '$url': {
        };
        '$post': V2FirebaseSubscribePost;
      };
    };
    'open': {
      'products': {
        '$url': {
        };
        '$get': V2OpenProductsGetHead;
        '$head': V2OpenProductsGetHead;
      };
      'pricing-table': {
        '$url': {
        };
        '$get': V2OpenPricingtableGetHead;
        '$head': V2OpenPricingtableGetHead;
      };
      'writing': {
        '$url': {
        };
        '$get': V2OpenWritingGetHead;
        '$head': V2OpenWritingGetHead;
      };
      'girlfriends': {
        '$url': {
        };
        '$get': V2OpenGirlfriendsGetHead;
        '$head': V2OpenGirlfriendsGetHead;
      };
      'test-smtp': {
        '$url': {
        };
        '$post': V2OpenTestsmtpPost;
      };
    };
    ':app_uid': {
      'subscriptions': {
        'get-embedded-checkout-session': {
          ':stripe_price_id': {
            ':quantity': {
              '$url': {
              };
              '$get': V2IdSubscriptionsGetembeddedcheckoutsessionIdIdGetHead;
              '$head': V2IdSubscriptionsGetembeddedcheckoutsessionIdIdGetHead;
            };
          };
        };
        'get-portal-session-url': {
          ':stripe_customer_id': {
            '$url': {
            };
            '$get': V2IdSubscriptionsGetportalsessionurlIdGetHead;
            '$head': V2IdSubscriptionsGetportalsessionurlIdGetHead;
          };
        };
      };
      'agents': {
        'duplicate': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdAgentsDuplicateIdGetHead;
            '$head': V2IdAgentsDuplicateIdGetHead;
          };
        };
        'duplicate-with-training': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdAgentsDuplicatewithtrainingIdGetHead;
            '$head': V2IdAgentsDuplicatewithtrainingIdGetHead;
          };
        };
        'update-image': {
          ':uid': {
            '$url': {
            };
            '$put': V2IdAgentsUpdateimageIdPut;
          };
        };
        'update-onboarding': {
          ':uid': {
            '$url': {
            };
            '$put': V2IdAgentsUpdateonboardingIdPut;
          };
        };
        'factory-settings': {
          '$url': {
          };
          '$get': V2IdAgentsFactorysettingsGetHead;
          '$head': V2IdAgentsFactorysettingsGetHead;
        };
        'factory-tools': {
          '$url': {
          };
          '$get': V2IdAgentsFactorytoolsGetHead;
          '$head': V2IdAgentsFactorytoolsGetHead;
        };
        '$url': {
        };
        '$get': V2IdAgentsGetHead;
        '$head': V2IdAgentsGetHead;
        ':uid': {
          '$url': {
          };
          '$get': V2IdAgentsIdGetHead;
          '$head': V2IdAgentsIdGetHead;
          '$put': V2IdAgentsIdPut;
          '$delete': V2IdAgentsIdDelete;
        };
        '$post': V2IdAgentsPost;
      };
      'chats': {
        '$url': {
        };
        '$get': V2IdChatsGetHead;
        '$head': V2IdChatsGetHead;
        'menu-history': {
          ':agent_uid': {
            '$url': {
            };
            '$get': V2IdChatsMenuhistoryIdGetHead;
            '$head': V2IdChatsMenuhistoryIdGetHead;
          };
        };
        'inbox-history': {
          ':agent_uid': {
            '$url': {
            };
            '$get': V2IdChatsInboxhistoryIdGetHead;
            '$head': V2IdChatsInboxhistoryIdGetHead;
          };
        };
        ':uid': {
          '$url': {
          };
          '$get': V2IdChatsIdGetHead;
          '$head': V2IdChatsIdGetHead;
          '$put': V2IdChatsIdPut;
          '$delete': V2IdChatsIdDelete;
        };
        '$post': V2IdChatsPost;
        'archive': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdChatsArchiveIdGetHead;
            '$head': V2IdChatsArchiveIdGetHead;
          };
        };
        'metadata': {
          ':uid': {
            '$url': {
            };
            '$put': V2IdChatsMetadataIdPut;
          };
        };
      };
      'providers': {
        '$url': {
        };
        '$get': V2IdProvidersGetHead;
        '$head': V2IdProvidersGetHead;
        ':uid': {
          '$url': {
          };
          '$get': V2IdProvidersIdGetHead;
          '$head': V2IdProvidersIdGetHead;
          '$put': V2IdProvidersIdPut;
          '$delete': V2IdProvidersIdDelete;
        };
        '$post': V2IdProvidersPost;
        'change-mode': {
          ':uid': {
            '$url': {
            };
            '$put': V2IdProvidersChangemodeIdPut;
          };
        };
        'ingest': {
          'urls': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersIngestUrlsIdPut;
            };
          };
          'youtube': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersIngestYoutubeIdPut;
            };
          };
          'documents': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersIngestDocumentsIdPut;
            };
          };
          'medias': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersIngestMediasIdPut;
            };
          };
          'quizzes': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersIngestQuizzesIdPut;
            };
          };
          'texts': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersIngestTextsIdPut;
            };
          };
          'products': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersIngestProductsIdPut;
            };
          };
          'storage': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersIngestStorageIdPut;
            };
          };
        };
        'fetch': {
          'urls': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersFetchUrlsIdPut;
            };
          };
          'sitemap': {
            ':uid': {
              '$url': {
              };
              '$put': V2IdProvidersFetchSitemapIdPut;
            };
          };
        };
        'transmit': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdProvidersTransmitIdGetHead;
            '$head': V2IdProvidersTransmitIdGetHead;
          };
        };
      };
      'sources': {
        '$url': {
        };
        '$get': V2IdSourcesGetHead;
        '$head': V2IdSourcesGetHead;
        ':uid': {
          '$url': {
          };
          '$get': V2IdSourcesIdGetHead;
          '$head': V2IdSourcesIdGetHead;
          '$delete': V2IdSourcesIdDelete;
        };
        'refresh': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdSourcesRefreshIdGetHead;
            '$head': V2IdSourcesRefreshIdGetHead;
          };
        };
      };
      'users': {
        '$url': {
        };
        '$get': V2IdUsersGetHead;
        '$head': V2IdUsersGetHead;
        ':uid': {
          '$url': {
          };
          '$get': V2IdUsersIdGetHead;
          '$head': V2IdUsersIdGetHead;
          '$put': V2IdUsersIdPut;
          '$delete': V2IdUsersIdDelete;
        };
        '$post': V2IdUsersPost;
        'change-infos': {
          '$url': {
          };
          '$post': V2IdUsersChangeinfosPost;
        };
        'change-email': {
          '$url': {
          };
          '$post': V2IdUsersChangeemailPost;
        };
        'change-password': {
          '$url': {
          };
          '$post': V2IdUsersChangepasswordPost;
        };
        'send-new-password': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdUsersSendnewpasswordIdGetHead;
            '$head': V2IdUsersSendnewpasswordIdGetHead;
          };
        };
      };
      'contacts': {
        '$url': {
        };
        '$get': V2IdContactsGetHead;
        '$head': V2IdContactsGetHead;
        ':uid': {
          '$url': {
          };
          '$get': V2IdContactsIdGetHead;
          '$head': V2IdContactsIdGetHead;
          '$put': V2IdContactsIdPut;
          '$delete': V2IdContactsIdDelete;
        };
        '$post': V2IdContactsPost;
      };
      'apps': {
        'settings': {
          'auto-topup': {
            '$url': {
            };
            '$post': V2IdAppsSettingsAutotopupPost;
          };
        };
      };
      'stats': {
        'all': {
          ':agent_uid?': {
            '$url': {
            };
            '$get': V2IdStatsAllIdGetHead;
            '$head': V2IdStatsAllIdGetHead;
          };
        };
      };
      'whatsapp': {
        'get-session': {
          ':agent_uid': {
            '$url': {
            };
            '$get': V2IdWhatsappGetsessionIdGetHead;
            '$head': V2IdWhatsappGetsessionIdGetHead;
          };
        };
        'delete-session': {
          ':agent_uid': {
            '$url': {
            };
            '$delete': V2IdWhatsappDeletesessionIdDelete;
          };
        };
        'get-qr-code': {
          ':agent_uid': {
            '$url': {
            };
            '$post': V2IdWhatsappGetqrcodeIdPost;
          };
        };
      };
      'feedbacks': {
        'mark-as-trained': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdFeedbacksMarkastrainedIdGetHead;
            '$head': V2IdFeedbacksMarkastrainedIdGetHead;
          };
        };
        '$url': {
        };
        '$get': V2IdFeedbacksGetHead;
        '$head': V2IdFeedbacksGetHead;
        ':uid': {
          '$url': {
          };
          '$get': V2IdFeedbacksIdGetHead;
          '$head': V2IdFeedbacksIdGetHead;
          '$delete': V2IdFeedbacksIdDelete;
        };
      };
      'copyrights': {
        '$url': {
        };
        '$get': V2IdCopyrightsGetHead;
        '$head': V2IdCopyrightsGetHead;
        '$post': V2IdCopyrightsPost;
        ':uid': {
          '$url': {
          };
          '$get': V2IdCopyrightsIdGetHead;
          '$head': V2IdCopyrightsIdGetHead;
          '$put': V2IdCopyrightsIdPut;
          '$delete': V2IdCopyrightsIdDelete;
        };
        'assign': {
          ':uid': {
            '$url': {
            };
            '$post': V2IdCopyrightsAssignIdPost;
          };
        };
        'unassign': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdCopyrightsUnassignIdGetHead;
            '$head': V2IdCopyrightsUnassignIdGetHead;
          };
        };
        'primary': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdCopyrightsPrimaryIdGetHead;
            '$head': V2IdCopyrightsPrimaryIdGetHead;
          };
        };
      };
      'domains': {
        '$url': {
        };
        '$get': V2IdDomainsGetHead;
        '$head': V2IdDomainsGetHead;
        '$post': V2IdDomainsPost;
        ':uid': {
          '$url': {
          };
          '$get': V2IdDomainsIdGetHead;
          '$head': V2IdDomainsIdGetHead;
          '$put': V2IdDomainsIdPut;
          '$delete': V2IdDomainsIdDelete;
        };
        'assign': {
          ':uid': {
            '$url': {
            };
            '$post': V2IdDomainsAssignIdPost;
          };
        };
        'unassign': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdDomainsUnassignIdGetHead;
            '$head': V2IdDomainsUnassignIdGetHead;
          };
        };
        'primary': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdDomainsPrimaryIdGetHead;
            '$head': V2IdDomainsPrimaryIdGetHead;
          };
        };
        'verify': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdDomainsVerifyIdGetHead;
            '$head': V2IdDomainsVerifyIdGetHead;
          };
        };
        'update-image': {
          ':uid': {
            '$url': {
            };
            '$put': V2IdDomainsUpdateimageIdPut;
          };
        };
      };
      'smtps': {
        '$url': {
        };
        '$get': V2IdSmtpsGetHead;
        '$head': V2IdSmtpsGetHead;
        '$post': V2IdSmtpsPost;
        ':uid': {
          '$url': {
          };
          '$get': V2IdSmtpsIdGetHead;
          '$head': V2IdSmtpsIdGetHead;
          '$put': V2IdSmtpsIdPut;
          '$delete': V2IdSmtpsIdDelete;
        };
        'assign': {
          ':uid': {
            '$url': {
            };
            '$post': V2IdSmtpsAssignIdPost;
          };
        };
        'unassign': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdSmtpsUnassignIdGetHead;
            '$head': V2IdSmtpsUnassignIdGetHead;
          };
        };
        'primary': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdSmtpsPrimaryIdGetHead;
            '$head': V2IdSmtpsPrimaryIdGetHead;
          };
        };
        'verify': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdSmtpsVerifyIdGetHead;
            '$head': V2IdSmtpsVerifyIdGetHead;
          };
        };
      };
      'keys': {
        '$url': {
        };
        '$get': V2IdKeysGetHead;
        '$head': V2IdKeysGetHead;
        '$post': V2IdKeysPost;
        ':uid': {
          '$url': {
          };
          '$get': V2IdKeysIdGetHead;
          '$head': V2IdKeysIdGetHead;
          '$put': V2IdKeysIdPut;
          '$delete': V2IdKeysIdDelete;
        };
        'assign': {
          ':uid': {
            '$url': {
            };
            '$post': V2IdKeysAssignIdPost;
          };
        };
        'unassign': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdKeysUnassignIdGetHead;
            '$head': V2IdKeysUnassignIdGetHead;
          };
        };
        'primary': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdKeysPrimaryIdGetHead;
            '$head': V2IdKeysPrimaryIdGetHead;
          };
        };
        'verify': {
          ':uid': {
            '$url': {
            };
            '$get': V2IdKeysVerifyIdGetHead;
            '$head': V2IdKeysVerifyIdGetHead;
          };
        };
        'test': {
          '$url': {
          };
          '$post': V2IdKeysTestPost;
        };
      };
      'tools': {
        '$url': {
        };
        '$get': V2IdToolsGetHead;
        '$head': V2IdToolsGetHead;
        ':uid': {
          '$url': {
          };
          '$get': V2IdToolsIdGetHead;
          '$head': V2IdToolsIdGetHead;
          '$put': V2IdToolsIdPut;
          '$delete': V2IdToolsIdDelete;
        };
        '$post': V2IdToolsPost;
        'toggle': {
          ':uid': {
            '$url': {
            };
            '$patch': V2IdToolsToggleIdPatch;
          };
        };
      };
    };
  };
}
const routes = [
] as const;
export const api = {
  routes,
  definition: {} as ApiDefinition
}
