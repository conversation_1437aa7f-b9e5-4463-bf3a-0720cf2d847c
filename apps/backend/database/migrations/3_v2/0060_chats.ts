import { BaseSchema } from '@adonisjs/lucid/schema'
import { addColumnIfNotExists, dropColumnIfExists } from '../../v2_functions.js'

export default class extends BaseSchema {
  protected tableName = 'chats'

  async up() {
    console.log('Starting chats table migration...')

    // Drop old columns
    const columnsToDrop = ['avatar_location', 'agents', 'analysis', 'model_uid']

    for (const column of columnsToDrop) {
      await dropColumnIfExists(this.db, this.tableName, column)
    }

    await addColumnIfNotExists(this.db, this.tableName, 'user_uid', 'uuid', 'NULL')
    await addColumnIfNotExists(this.db, this.tableName, 'loaded_context', 'text', 'NULL')
    await addColumnIfNotExists(this.db, this.tableName, 'loaded_variables', 'jsonb', 'NULL')
    await addColumnIfNotExists(this.db, this.tableName, 'loaded_user', 'jsonb', 'NULL')
  }

  async down() {
    console.log('Rolling back chats table migration...')
    // Add rollback logic if needed
  }
}
