import { BaseSchema } from '@adonisjs/lucid/schema'
import { addColumnIfNotExists } from '../../v2_functions.js'

export default class extends BaseSchema {
  protected tableName = 'messages'

  async up() {
    console.log('Starting messages table migration...')

    await addColumnIfNotExists(this.db, this.tableName, 'user_uid', 'uuid', 'NULL')
  }

  async down() {
    console.log('Rolling back messages table migration...')
    // Add rollback logic if needed
  }
}
