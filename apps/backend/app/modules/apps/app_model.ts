import LimitReachedException from '#app/exceptions/limit_reached'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import emitter from '@adonisjs/core/services/emitter'
import {
  BaseModel,
  beforeCreate,
  beforeFind,
  column,
  computed,
  hasMany,
  hasOne,
  manyToMany,
  SnakeCaseNamingStrategy,
} from '@adonisjs/lucid/orm'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import type { HasMany, HasOne, ManyToMany } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Contact from '#app/modules/contacts/contact_model'
import Feedback from '#app/modules/feedbacks/feedback_model'
import File from '#app/modules/files/file_model'
import Message from '#app/modules/messages/message_model'
import Provider from '#app/modules/rag/providers/provider_model'
import Source from '#app/modules/rag/sources/source_model'
import Subscription from '#app/modules/subscriptions/subscription_model'
import Tool from '#app/modules/tools/tool_model'
import Trace from '#app/modules/traces/trace_model'
import User from '#app/modules/users/user_model'
import Copyright from '#app/modules/whitelabel/copyrights/copyright_model'
import Domain from '#app/modules/whitelabel/domains/domain_model'
import Key from '#app/modules/whitelabel/keys/key_model'
import SMTP from '#app/modules/whitelabel/smtps/smtp_model'
import { appAutoTopupValidator } from '#app/modules/apps/app_validator'
import { providerTypesValidator } from '#app/modules/rag/providers/provider_validator'
import { subscriptionAddonsIdsValidator } from '#app/modules/subscriptions/subscription_validator'
import BaseFilter from '#app/modules/base/base_filter'

type StatsFiltersPayload = {
  start_date?: DateTime
  end_date?: DateTime
  agent_uid?: string
}

export default class App extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: App) {
    model.uid = uuid()
  }

  @manyToMany(() => User, {
    pivotTable: 'user_apps',
    localKey: 'uid',
    relatedKey: 'uid',
    pivotForeignKey: 'app_uid',
    pivotRelatedForeignKey: 'user_uid',
    pivotTimestamps: true,
  })
  declare users: ManyToMany<typeof User>

  @hasMany(() => Agent, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare agents: HasMany<typeof Agent>

  @hasMany(() => Chat, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare chats: HasMany<typeof Chat>

  @hasMany(() => Message, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare messages: HasMany<typeof Message>

  @hasMany(() => Provider, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare providers: HasMany<typeof Provider>

  @hasMany(() => Source, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare sources: HasMany<typeof Source>

  @hasMany(() => Contact, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare contacts: HasMany<typeof Contact>

  @hasMany(() => File, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare files: HasMany<typeof File>

  @hasMany(() => Feedback, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare feedbacks: HasMany<typeof Feedback>

  @hasOne(() => Subscription, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare subscriptions: HasOne<typeof Subscription>

  @hasMany(() => Tool, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare tools: HasMany<typeof Tool>

  @hasMany(() => Copyright, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare copyrights: HasMany<typeof Copyright>

  @hasMany(() => Domain, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare domains: HasMany<typeof Domain>

  @hasMany(() => SMTP, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare smtps: HasMany<typeof SMTP>

  @hasMany(() => Key, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare keys: HasMany<typeof Key>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({ meta: { searchable: true, type: 'number' } })
  declare available_credits: number

  @column({ meta: { searchable: false, type: 'string' } })
  declare logo: string

  @column({ meta: { searchable: false, type: 'string' } })
  declare favicon: string

  @column({ meta: { searchable: false, type: 'boolean' } })
  declare is_banned: boolean

  @column({ meta: { searchable: false, type: 'boolean' } })
  declare is_first_time_customer: boolean

  @column({ meta: { searchable: false, type: 'boolean' } })
  declare is_first_time_churning: boolean

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare auto_topup: Infer<typeof appAutoTopupValidator> | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare appsumo_license: string | null

  @column({ serializeAs: null, meta: { searchable: false, type: 'string' } })
  declare gclid: string | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare utm: object

  @column({ meta: { searchable: true, type: 'string' } })
  declare stripe_customer_id: string

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null

  @computed()
  get computed_files_path(): string {
    return `${env.get('NODE_ENV')}/${this.uid}`
  }

  async getOwner() {
    const app: App = this

    return app.related('users').query().whereIn('role', ['owner', 'admin']).firstOrFail()
  }

  async getAgentsCount() {
    const app: App = this
    const count = await app.related('agents').query().count('*', 'total').firstOrFail()

    return Number(count?.$extras?.total)
  }

  async getMessagesCount() {
    const app: App = this
    const count = await app.related('messages').query().count('*', 'total').firstOrFail()

    return Number(count?.$extras?.total)
  }

  async getSourcesCount(type: Infer<typeof providerTypesValidator>) {
    const app: App = this
    const query = app.related('sources').query()

    if (type) {
      query.where('type', type)
    }

    const count = await query.count('*', 'total').firstOrFail()

    return Number(count?.$extras?.total)
  }

  async getSeatsCount() {
    const app: App = this
    const count = await app.related('users').query().count('*', 'total').firstOrFail()

    return Number(count?.$extras?.total)
  }

  async getDomainsCount() {
    const app: App = this
    const count = await app.related('domains').query().count('*', 'total').firstOrFail()
    return Number(count?.$extras?.total)
  }

  async getSmtpsCount() {
    const app: App = this
    const count = await app.related('smtps').query().count('*', 'total').firstOrFail()
    return Number(count?.$extras?.total)
  }

  async getCopyrightsCount() {
    const app: App = this
    const count = await app.related('copyrights').query().count('*', 'total').firstOrFail()
    return Number(count?.$extras?.total)
  }

  async getPrimarySMTP() {
    const app: App = this

    return app.related('smtps').query().where('is_primary', true).first()
  }

  async getPrimaryDomain() {
    const app: App = this

    return app.related('domains').query().where('is_primary', true).first()
  }

  async getPrimaryCopyright() {
    const app: App = this

    return app.related('copyrights').query().where('is_primary', true).first()
  }

  async canConsumeAgents(params: { throwError: boolean }) {
    const { throwError } = params
    const { product } = this.subscriptions?.getPlan() || {}
    const { subscription } = this.subscriptions?.getAddon({ addonId: 'agents' }) || {}
    const planAvailable = product?.limitations?.agents || 0
    const addonAvailable = subscription?.app_product_quantity || 0
    const totalAvailable = planAvailable + addonAvailable
    const consumed = await this.getAgentsCount()
    const leftover = Math.max(0, totalAvailable - consumed)

    if (leftover <= 0) {
      if (throwError) {
        throw new LimitReachedException('agents')
      }

      return false
    }

    return true
  }

  async canConsumeCredits(params: { throwError: boolean; toConsume: number }) {
    const { throwError, toConsume } = params
    const totalAvailable = this.available_credits
    const leftover = Math.max(0, totalAvailable - toConsume)

    if (leftover <= 0) {
      if (throwError) {
        throw new LimitReachedException('credits')
      }

      return false
    }

    emitter.emit('credits:subtract', { app: this, account: null, credits: toConsume })

    return true
  }

  async canConsumeSeats(params: { throwError: boolean }) {
    const { throwError } = params
    const { product } = this.subscriptions?.getPlan() || {}
    const { subscription } = this.subscriptions?.getAddon({ addonId: 'seats' }) || {}
    const planAvailable = product?.limitations?.seats || 0
    const addonAvailable = subscription?.app_product_quantity || 0
    const totalAvailable = planAvailable + addonAvailable
    const consumed = await this.getSeatsCount()
    const leftover = Math.max(0, totalAvailable - consumed)

    if (leftover <= 0) {
      if (throwError) {
        throw new LimitReachedException('seats')
      }

      return false
    }

    return true
  }

  async canConsumeSources(params: {
    sourceType: Infer<typeof providerTypesValidator>
    throwError: boolean
    toConsume: number
  }) {
    const { throwError, toConsume, sourceType } = params
    const addonId = sourceType as Infer<typeof subscriptionAddonsIdsValidator>
    const { product } = this.subscriptions?.getPlan() || {}
    const { subscription } = this.subscriptions?.getAddon({ addonId }) || {}
    const planAvailable = product?.limitations?.knowledge[sourceType] || 0
    const addonAvailable = subscription?.app_product_quantity || 0
    const totalAvailable = planAvailable + addonAvailable
    const consumed = await this.getSourcesCount(sourceType)
    const leftover = Math.max(0, totalAvailable - consumed - toConsume)

    if (leftover <= 0) {
      if (throwError) {
        throw new LimitReachedException(sourceType)
      }

      return false
    }

    return true
  }

  @beforeFind()
  static async preloadRelationships(query: ModelQueryBuilderContract<typeof App>) {
    await query.preload('subscriptions')
  }

  private _applyCommonFilters<T extends typeof BaseModel>(
    query: ModelQueryBuilderContract<T>,
    payload: StatsFiltersPayload
  ) {
    const { start_date, end_date, agent_uid } = payload

    if (start_date) {
      query.where('created_at', '>=', start_date.toISO() as string)
    }

    if (end_date) {
      query.where('created_at', '<=', end_date.toISO() as string)
    }

    if (agent_uid) {
      query.where('agent_uid', agent_uid)
    }

    return query
  }

  async getSourceCounts(this: App, payload: StatsFiltersPayload) {
    const query = this.related('sources').query()
    this._applyCommonFilters(query, payload)

    const counts = await query.count('* as count').groupBy('type')

    let total = 0
    const result: { [key in Infer<typeof providerTypesValidator>]?: number } = {}

    for (const row of counts) {
      const count = Number(row.$extras.count || 0)
      result[row.type] = count
      total += count
    }

    return { result, total }
  }

  async getChatCount(this: App, payload: StatsFiltersPayload) {
    const query = this.related('chats').query()

    this._applyCommonFilters(query, payload)

    const countResult = await query.count('* as total').first()

    return Number(countResult?.$extras.total || 0)
  }

  async getContactCount(this: App, payload: StatsFiltersPayload) {
    const query = this.related('contacts').query()

    this._applyCommonFilters(query, payload)

    const countResult = await query.count('* as total').first()

    return Number(countResult?.$extras.total || 0)
  }

  async getMessageCount(this: App, payload: StatsFiltersPayload) {
    const query = this.related('messages').query()

    this._applyCommonFilters(query, payload)

    const countResult = await query.count('* as total').first()

    return Number(countResult?.$extras.total || 0)
  }

  async getFeedbackCount(this: App, payload: StatsFiltersPayload) {
    const query = this.related('feedbacks').query()

    this._applyCommonFilters(query, payload)

    const countResult = await query.count('* as total').first()

    return Number(countResult?.$extras.total || 0)
  }

  async getFileCount(this: App, payload: StatsFiltersPayload) {
    const query = this.related('files').query()

    this._applyCommonFilters(query, payload)

    const countResult = await query.count('* as total').first()

    return Number(countResult?.$extras.total || 0)
  }

  async getTotalCreditsUsed(payload: StatsFiltersPayload) {
    const query = Trace.query().where('app_uid', this.uid)

    this._applyCommonFilters(query, payload)

    const result = await query.sum('credits as total_credits').first()

    return Number(result?.$extras.total_credits || 0)
  }

  async getResolvedChatCount(this: App, payload: StatsFiltersPayload) {
    const query = this.related('chats').query().where('is_resolve', true)

    this._applyCommonFilters(query, payload)

    const countResult = await query.count('* as total').first()

    return Number(countResult?.$extras.total || 0)
  }
}
