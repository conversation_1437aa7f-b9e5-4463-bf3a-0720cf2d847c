import vine from '@vinejs/vine'

const metadataSchema = vine.object({}).allowUnknownProperties()

const loadedUserSchema = vine.object({
  // Standard headers
  referer: vine.string().optional(),
  origin: vine.string().optional(),
  // Client hints (browser & device)
  sec_ch_ua: vine.string().optional(),
  sec_ch_ua_mobile: vine.string().optional(),
  sec_ch_ua_arch: vine.string().optional(),
  sec_ch_ua_bitness: vine.string().optional(),
  sec_ch_ua_full_version: vine.string().optional(),
  sec_ch_ua_full_version_list: vine.string().optional(),
  sec_ch_ua_platform: vine.string().optional(),
  sec_ch_ua_platform_version: vine.string().optional(),
  sec_ch_ua_model: vine.string().optional(),
  // Navigator & performance
  cookie_enabled: vine.boolean().optional(),
  languages: vine.array(vine.string()).optional(),
  device_memory: vine.number().optional(),
  max_touch_points: vine.number().optional(),
  device_pixel_ratio: vine.number().optional(),
  // Screen properties
  screen_width: vine.number().optional(),
  screen_height: vine.number().optional(),
  avail_width: vine.number().optional(),
  avail_height: vine.number().optional(),
  orientation_type: vine.string().optional(),
  orientation_angle: vine.number().optional(),
  // Network & connection
  network_type: vine.string().optional(),
  network_downlink: vine.number().optional(),
  network_rtt: vine.number().optional(),
  network_save_data: vine.boolean().optional(),
  // Time & locale
  timezone: vine.string().optional(),
  timezone_offset: vine.number().optional(),
  time_zone: vine.string().optional(),
  locale_date: vine.string().optional(),
  locale_number: vine.string().optional(),
  // Vendor & product
  vendor: vine.string().optional(),
  product: vine.string().optional(),
  product_sub: vine.string().optional(),
  // Geolocation from CF headers
  ip: vine.string().optional(),
  city: vine.string().optional(),
  region: vine.string().optional(),
  country: vine.string().optional(),
  continent: vine.string().optional(),
  postal_code: vine.string().optional(),
  latitude: vine.string().optional(),
  longitude: vine.string().optional(),
  // Application context
  utm_source: vine.string().optional(),
  utm_medium: vine.string().optional(),
  utm_campaign: vine.string().optional(),
  utm_term: vine.string().optional(),
  utm_content: vine.string().optional(),
  // Page
  page_path: vine.string().optional(),
  page_query: vine.string().optional(),
})

const storeSchema = vine.object({
  agent_uid: vine.string().uuid(),
  user_uid: vine.string().uuid().nullable(),
  label: vine.string().trim().optional(),
  loaded_context: vine.string().trim().optional(),
  loaded_variables: vine.object({}).allowUnknownProperties(),
  loaded_user: loadedUserSchema,
  metadata: metadataSchema.optional(),
})

const updateSchema = vine.object({
  ...storeSchema.getProperties(),
  is_hidden: vine.boolean().optional(),
  is_archived: vine.boolean().optional(),
  is_favorite: vine.boolean().optional(),
  is_resolve: vine.boolean().optional(),
  is_email: vine.boolean().optional(),
  is_whatsapp: vine.boolean().optional(),
  has_feedback: vine.boolean().optional(),
  has_contact: vine.boolean().optional(),
  has_user_tagging: vine.boolean().optional(),
  has_messages: vine.boolean().optional(),
  is_ai_disabled: vine.boolean().optional(),
})

const metadataUpdateSchema = vine.object({}).allowUnknownProperties()

const historySchema = vine.object({
  page: vine.number(),
  search: vine.string().optional(),
  is_archived: vine.boolean().optional(),
  is_favorite: vine.boolean().optional(),
  is_resolve: vine.boolean().optional(),
  is_email: vine.boolean().optional(),
  is_whatsapp: vine.boolean().optional(),
  has_feedback: vine.boolean().optional(),
  has_contact: vine.boolean().optional(),
})

const chatStoreValidator = vine.compile(storeSchema)
const chatUpdateValidator = vine.compile(updateSchema)
const chatMetadataValidator = vine.compile(metadataSchema)
const chatMetadataUpdateValidator = vine.compile(metadataUpdateSchema)
const chatLoadedUserValidator = vine.compile(loadedUserSchema)
const chatHistoryValidator = vine.compile(historySchema)

export {
  chatStoreValidator,
  chatUpdateValidator,
  chatMetadataValidator,
  chatMetadataUpdateValidator,
  chatLoadedUserValidator,
  chatHistoryValidator,
}
