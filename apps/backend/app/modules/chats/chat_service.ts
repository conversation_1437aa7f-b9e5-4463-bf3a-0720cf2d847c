import { <PERSON><PERSON><PERSON><PERSON>el<PERSON> } from '#app/helpers'
import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { DateTime } from 'luxon'
import { mergeAndConcat } from 'merge-anything'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import { HasManyQueryBuilderContract } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import { chatStoreValidator, chatUpdateValidator } from '#app/modules/chats/chat_validator'
import ChatPresenter from '#app/modules/chats/chat_presenter'
import User from '../users/user_model.js'
import Chat from './chat_model.js'

@inject()
export default class ChatService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1

    return await this.ctx.app
      .related('chats')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()
  }

  async store(payload: Infer<typeof chatStoreValidator>) {
    // Convert null to undefined for user_uid to match model expectations
    const normalizedPayload = {
      ...payload,
      user_uid: payload.user_uid || undefined,
    }

    return await this.ctx.app.related('chats').create(normalizedPayload)
  }

  async update(uid: string, payload: Infer<typeof chatUpdateValidator>) {
    const chat = await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()

    return await chat.merge(payload).save()
  }

  async destroy(uid: string) {
    const chat = await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()

    return await chat.delete()
  }

  async search(params: {
    agentUid: string
    query: string
    filter: Infer<typeof requestFilterValidator>
  }) {
    const { agentUid, query, filter } = params
    const page = filter.page || 1

    if (!query) {
      return this.ctx.app
        .related('chats')
        .query()
        .where('agent_uid', agentUid)
        .whereRaw('1 = 0') // Condition that is always false
        .paginate(page, env.get('APP_PAGINATION_LIMIT'))
    }

    const isUUID = ValidationHelper.isUUID(query)

    const dbQuery = this.ctx.app
      .related('chats')
      .query()
      .where('agent_uid', agentUid)
      .where('has_messages', true)
      .andWhere((builder) => {
        if (isUUID) {
          builder.where('uid', '=', query)
        } else {
          builder.where('external_id', 'ILIKE', `%${query}%`)
        }

        builder
          .orWhere('external_id', 'ILIKE', `%${query}%`)
          .orWhereHas('messages', (messageQuery) => {
            messageQuery.where('output_text', 'ILIKE', `%${query}%`)
          })
          .orWhereHas('contacts', (contactQuery) => {
            contactQuery
              .where('first_name', 'ILIKE', `%${query}%`)
              .orWhere('last_name', 'ILIKE', `%${query}%`)
              .orWhere('email', 'ILIKE', `%${query}%`)
              .orWhere('phone', 'ILIKE', `%${query}%`)
          })
      })
      .filter(filter)

    return await dbQuery.paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async archive(uid: string) {
    const chat = await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()

    await chat.merge({ is_archived: true }).save()
  }

  async appendMetadata(uid: string, metadata: object) {
    const chat = await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()

    const merged_metadata = chat?.metadata ? mergeAndConcat(chat?.metadata, metadata) : metadata

    return await chat.merge({ metadata: merged_metadata }).save()
  }

  async historyGroupedByTimeframe(agentUid: string, filters: { search?: string }) {
    const { search } = filters

    const agent = await this.ctx.app.related('agents').query().where('uid', agentUid).firstOrFail()
    const userUid = this.ctx.auth?.user?.uid

    if (!userUid) {
      throw new Error('User not found')
    }

    console.log('DEBUG: Agent found:', agent.uid)

    // First, let's check if there are ANY chats for this agent
    const allChats = await agent
      .related('chats')
      .query()
      .where('user_uid', userUid)
      .select('uid', 'is_hidden', 'has_messages', 'is_archived', 'updated_at')

    console.log('DEBUG: Total chats for agent:', allChats.length)
    console.log(
      'DEBUG: Chat details:',
      allChats.map((c) => ({
        uid: c.uid,
        is_hidden: c.is_hidden,
        has_messages: c.has_messages,
        is_archived: c.is_archived,
        updated_at: c.updated_at,
      }))
    )

    // Build base query
    let chatsQuery = agent
      .related('chats')
      .query()
      .select('uid', 'label', 'is_favorite', 'is_resolve', 'is_email', 'is_whatsapp', 'updated_at')
      .where('is_hidden', false)
      .where('is_archived', false)
      .orderBy('updated_at', 'desc')

    console.log('DEBUG: search', search)

    // Test the base query without search first
    const baseResults = await chatsQuery.clone()
    console.log('DEBUG: Base query results count:', baseResults.length)
    console.log(
      'DEBUG: Base query results:',
      baseResults.map((c) => ({
        uid: c.uid,
        updated_at: c.updated_at,
      }))
    )

    // Apply search filter if provided
    if (search) {
      console.log('DEBUG: Applying search filter')

      const uuids = await this.getChatsFilteredBySearch(agent, search)
      console.log('DEBUG: Search filtered UUIDs:', uuids)

      if (uuids.length === 0) {
        console.log('DEBUG: No UUIDs found for search, returning empty')
        return {
          today: [],
          yesterday: [],
          week: [],
          month: [],
          older: [],
        }
      }

      chatsQuery = chatsQuery.whereIn('uid', uuids)
    }

    console.log('DEBUG: About to group by timeframe')
    return await this.groupChatsByTimeframeSQL(chatsQuery)
  }

  async history(
    agentUid: string,
    filters: {
      page: number
      search?: string
      is_archived?: boolean
      is_favorite?: boolean
      is_resolve?: boolean
      is_email?: boolean
      is_whatsapp?: boolean
      has_feedback?: boolean
      has_contact?: boolean
    }
  ) {
    const {
      page,
      search,
      is_archived,
      is_resolve,
      is_favorite,
      is_email,
      is_whatsapp,
      has_feedback,
      has_contact,
    } = filters

    const agent = await this.ctx.app.related('agents').query().where('uid', agentUid).firstOrFail()

    // Build base query
    let chatsQuery = agent
      .related('chats')
      .query()
      .select('uid', 'label', 'is_favorite', 'is_resolve', 'is_email', 'is_whatsapp', 'updated_at')
      .where('is_hidden', false)
      .orderBy('updated_at', 'desc')

    if (is_archived) {
      chatsQuery = chatsQuery.where('is_archived', true)
    }

    if (is_favorite) {
      chatsQuery = chatsQuery.where('is_favorite', true)
    }

    if (is_resolve) {
      chatsQuery = chatsQuery.where('is_resolve', true)
    }

    if (is_email) {
      chatsQuery = chatsQuery.where('is_email', true)
    }

    if (is_whatsapp) {
      chatsQuery = chatsQuery.where('is_whatsapp', true)
    }

    if (has_feedback) {
      chatsQuery = chatsQuery.where('has_feedback', true)
    }

    if (has_contact) {
      chatsQuery = chatsQuery.where('has_contact', true)
    }

    // Apply search filter
    if (search) {
      const uuids = await this.getChatsFilteredBySearch(agent, search)

      if (uuids.length === 0) {
        return this.ctx.app
          .related('chats')
          .query()
          .where('agent_uid', agentUid)
          .whereRaw('1 = 0')
          .paginate(page, env.get('APP_PAGINATION_LIMIT'))
      }

      chatsQuery = chatsQuery.whereIn('uid', uuids)
    }

    return await chatsQuery.paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  private async getChatsFilteredBySearch(agent: Agent, search: string): Promise<string[]> {
    const isUUID = ValidationHelper.isUUID(search)

    const [messages, chats] = await Promise.all([
      agent
        .related('messages')
        .query()
        .where((query: any) => {
          if (isUUID) {
            query.where('uid', search)
          } else {
            query.where('output_text', 'ILIKE', `%${search}%`)
          }
        })
        .distinct('chat_uid')
        .select('chat_uid'),
      agent
        .related('chats')
        .query()
        .where((query: any) => {
          if (isUUID) {
            query.where('uid', search)
          } else {
            query.whereRaw('metadata::text ILIKE ?', [`%${search}%`])
          }
        })
        .select('uid'),
    ])

    return Array.from(
      new Set([
        ...messages.map((item: any) => item.chat_uid),
        ...chats.map((item: any) => item.uid),
      ])
    )
  }

  private async groupChatsByTimeframeSQL(
    baseQuery: HasManyQueryBuilderContract<typeof Chat, Chat>
  ) {
    const now = DateTime.now()
    const today = now.startOf('day').toJSDate()
    const yesterday = now.minus({ days: 1 }).startOf('day').toJSDate()
    const week = now.minus({ weeks: 1 }).startOf('day').toJSDate()
    const month = now.minus({ months: 1 }).startOf('day').toJSDate()

    console.log('DEBUG: Date boundaries:')
    console.log('  now:', now.toISO())
    console.log('  today:', today)
    console.log('  yesterday:', yesterday)
    console.log('  week:', week)
    console.log('  month:', month)

    // First let's see what the base query returns without any date filtering
    const allBaseResults = await baseQuery.clone()
    console.log('DEBUG: All base results count:', allBaseResults.length)
    console.log(
      'DEBUG: All base results dates:',
      allBaseResults.map((c) => ({
        uid: c.uid,
        updated_at: c.updated_at,
      }))
    )

    // Execute parallel queries for each timeframe - clone baseQuery for each to avoid stacking conditions
    const [todayChats, yesterdayChats, weekChats, monthChats, olderChats] = await Promise.all([
      // Today
      baseQuery.clone().where('updated_at', '>=', today),

      // Yesterday
      baseQuery.clone().where('updated_at', '>=', yesterday).where('updated_at', '<', today),

      // This week
      baseQuery.clone().where('updated_at', '>=', week).where('updated_at', '<', yesterday),

      // This month
      baseQuery.clone().where('updated_at', '>=', month).where('updated_at', '<', week),

      // Older
      baseQuery.clone().where('updated_at', '<', month),
    ])

    console.log('DEBUG: Timeframe results:')
    console.log('  today:', todayChats.length)
    console.log('  yesterday:', yesterdayChats.length)
    console.log('  week:', weekChats.length)
    console.log('  month:', monthChats.length)
    console.log('  older:', olderChats.length)

    return {
      today: todayChats,
      yesterday: yesterdayChats,
      week: weekChats,
      month: monthChats,
      older: olderChats,
    }
  }
}
