import env from '#start/env'
import type { Infer } from '@vinejs/vine/types'
import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { ChatCompletionMessageParam } from 'openai/resources/index.mjs'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import string from '@adonisjs/core/helpers/string'
import {
  beforeCreate,
  belongsTo,
  column,
  computed,
  hasMany,
  SnakeCaseNamingStrategy,
} from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Contact from '#app/modules/contacts/contact_model'
import Message from '#app/modules/messages/message_model'
import Model from '#app/modules/models/model_model'
import Trace from '#app/modules/traces/trace_model'
import User from '#app/modules/users/user_model'
import { chatLoadedUserValidator } from '#app/modules/chats/chat_validator'
import BaseFilter from '#app/modules/base/base_filter'

export default class Chat extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Chat) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @belongsTo(() => User, {
    localKey: 'uid',
    foreignKey: 'user_uid',
  })
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Agent, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare agent: BelongsTo<typeof Agent>

  @belongsTo(() => Model, {
    localKey: 'uid',
    foreignKey: 'model_uid',
  })
  declare model: BelongsTo<typeof Model>

  @hasMany(() => Message, { serializeAs: null, localKey: 'uid', foreignKey: 'chat_uid' })
  declare messages: HasMany<typeof Message>

  @hasMany(() => Contact, { serializeAs: null, localKey: 'uid', foreignKey: 'chat_uid' })
  declare contacts: HasMany<typeof Contact>

  @hasMany(() => Trace, { serializeAs: null, localKey: 'uid', foreignKey: 'chat_uid' })
  declare traces: HasMany<typeof Trace>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare user_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare agent_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
    },
  })
  declare external_id: string | null

  @column({
    prepare: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    consume: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare label: string

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_hidden: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_archived: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_favorite: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_resolve: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_email: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_whatsapp: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare has_feedback: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare has_contact: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare has_user_tagging: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare has_messages: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_ai_disabled: boolean

  @column({ meta: { searchable: true, type: 'string' } })
  declare loaded_context: string

  @column({ meta: { searchable: true, type: 'object' } })
  declare loaded_variables: { [key: string]: any } | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare loaded_user: Infer<typeof chatLoadedUserValidator>

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null

  @computed()
  get computed_files_path(): string {
    return `${env.get('NODE_ENV')}/${this.app_uid}/${this.agent_uid}/${this.uid}`
  }

  @computed()
  get computed_has_user_tagging(): boolean {
    if (this.metadata?.user && Object.keys(this.metadata?.user).length) {
      const user = this.metadata?.user ?? {}

      if (user?.id || user?.email || user?.last_name || user?.first_name) {
        return true
      }
    }

    return false
  }

  async getChatMessages(): Promise<Message[]> {
    await this.load('messages', (query) => {
      query.orderBy('created_at', 'asc')
      query.preload('files')
      query.preload('feedback')
    })

    if (!this.messages || this.messages.length === 0) {
      return []
    }

    return this.messages
  }

  async getChatMessagesOpenAiFormat(): Promise<ChatCompletionMessageParam[]> {
    await this.load('messages', (query) => {
      query.orderBy('created_at', 'asc')
      query.preload('files')
      query.preload('feedback')
    })

    if (!this.messages || this.messages.length === 0) {
      return []
    }

    return this.messages.map((message) => ({
      role: message.role as 'user' | 'assistant' | 'developer' | 'function',
      content: message.output_text,
      name: message.role === 'user' ? 'You' : 'Assistant',
    }))
  }

  async getChatMessagesStringFormat(): Promise<string> {
    const messages = await this.getChatMessagesOpenAiFormat()

    if (!messages || messages.length === 0) {
      return ''
    }

    return messages
      .map((message) => {
        const role = message.role === 'user' ? 'You' : 'Assistant'
        const content = typeof message.content === 'string' ? message.content : ''

        return `${role}: ${string.sentenceCase(content)}`
      })
      .join('\n')
  }
}
