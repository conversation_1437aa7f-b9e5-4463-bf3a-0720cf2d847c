import AiEngine from '#app/ai/engine'
import <PERSON><PERSON><PERSON><PERSON><PERSON>el<PERSON> from '#app/ai/helpers/lite_llm_helper'
import { TraceHelper } from '#app/helpers'
import { COST_PER_AGENT } from '#config/credits'
import { Infer } from '@vinejs/vine/types'
import { mergeAndConcat } from 'merge-anything'
import { Readable } from 'node:stream'
import { inject } from '@adonisjs/core'
import { MultipartFile } from '@adonisjs/core/bodyparser'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Chat from '#app/modules/chats/chat_model'
import Feedback from '#app/modules/feedbacks/feedback_model'
import File from '#app/modules/files/file_model'
import Message from '#app/modules/messages/message_model'
import Model from '#app/modules/models/model_model'
import {
  embedCreateChatValidator,
  embedCreateMessageValidator,
  embedFeedbackValidator,
} from '#app/modules/embeds/embed_validator'

@inject()
export default class EmbedService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  /*
   * TTS
   */
  async textToSpeech(message_uid: string) {
    const message = await Message.query().where('uid', message_uid).firstOrFail()

    await Promise.all([message.load('app'), message.load('agent')])

    const { agent, app, output_text } = message
    const { voice } = agent

    await app.canConsumeCredits({
      throwError: true,
      toConsume: COST_PER_AGENT['synthesize'],
    })

    return await LiteLlmHelper.textToSpeech({
      app,
      input: output_text,
      voice,
    })
  }

  /*
   * STT
   */
  async speechToText(payload: { chat_uid: string; file_path: string }) {
    const { chat_uid, file_path } = payload

    const chat = await Chat.query().where('uid', chat_uid).firstOrFail()

    await chat.load('app')

    const { app } = chat

    await app.canConsumeCredits({
      throwError: true,
      toConsume: COST_PER_AGENT['transcribe'],
    })

    return await LiteLlmHelper.speechToText({
      app,
      filePath: file_path,
    })
  }

  /*
   * Chats
   */
  async getChat(chat_uid: string) {
    const chat = await Chat.query().where('uid', chat_uid).firstOrFail()
    const messages = await chat.getChatMessages()

    return {
      chat,
      messages,
    }
  }

  async createChat(payload: Infer<typeof embedCreateChatValidator>) {
    const { agent_uid, loaded_variables } = payload

    const agent = await Agent.findByOrFail('uid', agent_uid)

    const chatCount = await db.rawQuery(
      'SELECT count(*) as total FROM chats WHERE agent_uid = :agentUid',
      { agent_uid }
    )

    const chat = await Chat.create({
      app_uid: agent.app_uid,
      agent_uid: agent.uid,
      loaded_variables,
      label: String(Number.parseInt(chatCount.rows[0].total) + 1),
      is_hidden: agent.hide_inbox,
    })

    agent.prewarmIndex()

    return chat
  }

  /*
   * Password
   */
  async verifyPassword(payload: { uid: string; password: string }) {
    const { uid, password } = payload

    const agent = await Agent.findByOrFail('uid', uid)

    return { password_verified: agent.password === password }
  }

  /*
   * Feedbacks
   */
  async createFeedback(message_uid: string, payload: Infer<typeof embedFeedbackValidator>) {
    const { status } = payload

    const message = await Message.findByOrFail('uid', message_uid)
    await Promise.all([message.load('feedback'), message.load('chat')])

    const { chat, feedback } = message

    const chatMessages = await chat.getChatMessages()
    const currentMessageIndex = chatMessages.findIndex((msg) => msg.uid === message_uid)

    let userMessage = ''

    if (currentMessageIndex > 0) {
      userMessage = chatMessages[currentMessageIndex - 1].output_text
    }

    let savedFeedback

    if (feedback) {
      // Update existing feedback
      savedFeedback = await feedback
        .merge({
          status,
          user_message: userMessage,
          ai_message: message.output_text,
        })
        .save()
    } else {
      // Create new feedback
      savedFeedback = await Feedback.create({
        app_uid: message.app_uid,
        agent_uid: message.agent_uid,
        chat_uid: message.chat_uid,
        message_uid: message_uid,
        user_message: userMessage,
        ai_message: message.output_text,
        status,
        is_trained: false,
      })
    }

    await Promise.allSettled([
      TraceHelper.createTrace({
        appUid: message.app_uid,
        agentUid: message.agent_uid,
        chatUid: message.chat_uid,
        feedbackUid: savedFeedback.uid,
        trigger: 'chatbot',
        action: 'chatbot_feedback_create',
        ctx: this.ctx,
        credits: 0,
        sendWebhook: true,
      }),
      chat.merge({ has_feedback: true }).save(),
    ])

    return savedFeedback
  }

  /*
   * Agents
   */
  async getAgent(uid: string) {
    const agent = await Agent.findBy('uid', uid)

    if (!uid || !agent) {
      throw {
        message: 'Invalid agent',
        status: 400,
        code: 'base/invalid-agent-uid',
      }
    }

    await TraceHelper.createTrace({
      appUid: agent.app_uid,
      agentUid: agent.uid,
      trigger: 'chatbot',
      action: 'chatbot_open',
      ctx: this.ctx,
      credits: 0,
      sendWebhook: true,
    })

    return agent
  }

  /**
   * Files
   */
  async createFiles(payload: { chat_uid: string; files: MultipartFile[] }) {
    const { chat_uid, files } = payload

    const chat = await Chat.findByOrFail('uid', chat_uid)
    const _files = Array.isArray(files) ? files : [files]

    const savedFiles = await Promise.all(
      _files.map(async (file) => {
        return await File.create({
          app_uid: chat.app_uid,
          agent_uid: chat.agent_uid,
          chat_uid: chat.uid,
          name: file.meta.name,
          extname: file.meta.extname,
          location: file.meta.storage_path,
          size: file.meta.size,
          checksum: file.meta.checksum,
        })
      })
    )

    return savedFiles
  }

  async appendMetadata(payload: { uid: string; model: string; metadata: any }) {
    const { uid, model, metadata } = payload

    let row
    let mergeObj = {}

    switch (model) {
      case 'chats':
        row = await Chat.findByOrFail('uid', uid)
        mergeObj = { has_user_tagging: true }
        break
      default:
        throw new Error('Invalid model type')
    }

    const newMetadata = row.metadata ? mergeAndConcat(row.metadata, metadata) : metadata

    delete metadata?.uid
    delete metadata?.app_uid

    return row.merge({ ...mergeObj, metadata: newMetadata }).save()
  }

  /*
   * Messages
   */
  async createMessage(payload: Infer<typeof embedCreateMessageValidator>) {
    const {
      app_uid,
      user_uid,
      agent_uid,
      chat_uid,
      model_uid,
      input,
      role,
      images_urls = [],
      metadata = {},
    } = payload

    if (input && input.length > 20480) {
      throw {
        message: 'Input exceeds maximum allowed length of 20480 characters.',
      }
    }

    const stream = new Readable({
      objectMode: true,
      autoDestroy: false,
      read() {},
    })

    const [app, agent, chat, model] = await Promise.all([
      App.query().where('uid', app_uid).firstOrFail(),
      Agent.query().where('uid', agent_uid).andWhere('app_uid', app_uid).firstOrFail(),
      Chat.query().where('uid', chat_uid).andWhere('agent_uid', agent_uid).firstOrFail(),
      Model.query().where('uid', model_uid).firstOrFail(),
    ])

    if (chat.agent_uid !== agent_uid) {
      throw {
        message: 'Invalid chat',
        status: 400,
        code: 'base/invalid-chat-uid',
      }
    }

    const engine = new AiEngine({ app, agent, model, chat, stream })

    engine.execute({ input, role, imagesUrls: images_urls, metadata })

    return stream
  }
}
